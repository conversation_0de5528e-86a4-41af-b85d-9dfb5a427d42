/**
 * SubordinatedAlbum.java
 * com.ximalaya.ting.android.framework.model.album
 *
 * Function： TODO 
 *
 *   ver     date      		author
 * ──────────────────────────────────
 *   		 2015-7-17 		jack.qin
 *
 * Copyright (c) 2015, TNT All Rights Reserved.
 */

package com.ximalaya.ting.android.opensdk.model.album;

import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.opensdk.util.XiMaDataSupport;

/**
 * ClassName:SubordinatedAlbum Function: TODO ADD FUNCTION Reason: TODO ADD
 * REASON
 * 
 * <AUTHOR>
 * @version
 * @since Ver 1.1
 * @Date 2015-7-17 上午10:50:26
 * 
 * @see
 */
public class SubordinatedAlbum extends XiMaDataSupport implements Parcelable
{
	@SerializedName("id")
	private long albumId;
	@SerializedName("album_title")
	private String albumTitle;
	@SerializedName("cover_url_small")
	private String coverUrlSmall;
	@SerializedName("cover_url_middle")
	private String coverUrlMiddle;
	@SerializedName("cover_url_large")
	private String coverUrlLarge;
    @SerializedName(value = "type",alternate = {"albumType"})
    private int type;
	@SerializedName("isPodcastAlbum")
	private boolean isPodcastAlbum;
	private long uptoDateTime;
	private String recSrc;//新添加专辑字段
	private String recTrack;//新添加专辑字段
	private int serializeStatus;//1:连载中 2：完本 //

	private int preferredType;  // 0表示不是优先；1表示优先
	private int vipFreeType;//0:非免费  1：会员免费
	private boolean isVipFree;
	private boolean isPaid;
	private String albumSubscript;
	private String coverLarge;
	private long welistenAlbumId;
	private int freeListenStatus; // 畅听解锁状态，0不支持，1支持
	private int status; // 审核状态
	private boolean isPaidAlbum; // 是否付费专辑，baseinfo接口中albumInfo下面新增了isPaid字段，为了跟原来的isPaid区分（取自trackInfo里的isPaid），这里单独加个字段
	private int offlineType = -1;
	private boolean isChildVipAlbum = false;//是否儿童会员专辑

	public long getWelistenAlbumId() {
		return welistenAlbumId;
	}

	public void setWelistenAlbumId(long welistenAlbumId) {
		this.welistenAlbumId = welistenAlbumId;
	}
	
	public String getRecSrc() {
	    return recSrc;
	}
	
	public void setRecSrc(String recSrc) {
	    this.recSrc = recSrc;
	}
	
	public String getRecTrack() {
	    return recTrack;
	}
	
	public void setRecTrack(String recTrack) {
	    this.recTrack = recTrack;
	}
	public long getUptoDateTime() {
		return uptoDateTime;
	}

	public void setUptoDateTime(long uptoDateTime) {
		this.uptoDateTime = uptoDateTime;
	}
	public long getAlbumId()
	{
		return albumId;
	}

	public void setAlbumId(long albumId)
	{
		this.albumId = albumId;
	}

	public String getAlbumTitle()
	{
		return albumTitle;
	}

	public void setAlbumTitle(String albumTitle)
	{
		this.albumTitle = albumTitle;
	}

	public String getCoverUrlSmall()
	{
		return coverUrlSmall;
	}

	public void setCoverUrlSmall(String coverUrlSmall)
	{
		this.coverUrlSmall = coverUrlSmall;
	}

	public String getCoverUrlMiddle()
	{
		return coverUrlMiddle;
	}

	public void setCoverUrlMiddle(String coverUrlMiddle)
	{
		this.coverUrlMiddle = coverUrlMiddle;
	}

	public String getCoverUrlLarge()
	{
		return coverUrlLarge;
	}

	public void setCoverUrlLarge(String coverUrlLarge)
	{
		this.coverUrlLarge = coverUrlLarge;
	}

	public String getCoverLarge() {
		return coverLarge;
	}

	public void setCoverLarge(String coverLarge) {
		this.coverLarge = coverLarge;
	}

	public int getSerializeStatus() {
		return serializeStatus;
	}

	public void setSerializeStatus(int serializeStatus) {
		this.serializeStatus = serializeStatus;
	}

	public int getPreferredType() {
		return preferredType;
	}

	public void setPreferredType(int preferredType) {
		this.preferredType = preferredType;
	}

	public int getVipFreeType() {
		return vipFreeType;
	}

	public void setVipFreeType(int vipFreeType) {
		this.vipFreeType = vipFreeType;
	}

	public boolean isVipFree() {
		return isVipFree;
	}

	public void setVipFree(boolean vipFree) {
		isVipFree = vipFree;
	}

	public boolean isPaid() {
		return isPaid;
	}

	public void setPaid(boolean paid) {
		isPaid = paid;
	}

	public String getAlbumSubscript() {
		return albumSubscript;
	}

	public void setAlbumSubscript(String albumSubscript) {
		this.albumSubscript = albumSubscript;
	}

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

	public int getFreeListenStatus() {
		return freeListenStatus;
	}

	public void setFreeListenStatus(int freeListenStatus) {
		this.freeListenStatus = freeListenStatus;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public boolean isPaidAlbum() {
		return isPaidAlbum;
	}

	public void setPaidAlbum(boolean paidAlbum) {
		isPaidAlbum = paidAlbum;
	}

	public int getOfflineType() {
		return offlineType;
	}

	public void setOfflineType(int offlineType) {
		this.offlineType = offlineType;
	}

	public void setIsPodcastAlbum(boolean podcastAlbum) {
		isPodcastAlbum = podcastAlbum;
	}

	public boolean isPodcastAlbum() {
		return isPodcastAlbum;
	}

	public boolean isChildVipAlbum() {
		return isChildVipAlbum;
	}

	public void setChildVipAlbum(boolean childVipAlbum) {
		isChildVipAlbum = childVipAlbum;
	}

	@Override
	public String toString()
	{
		return "SubordinatedAlbum [albumId=" + albumId + ", albumTitle="
				+ albumTitle + ", coverUrlSmall=" + coverUrlSmall
				+ ", coverUrlMiddle=" + coverUrlMiddle + ", coverUrlLarge="
				+ coverUrlLarge + ", recSrc="
				+ recSrc +", recTrack="
				+ recTrack +",serializeStatus="
				+ serializeStatus
				+ ", freeListenStatus" + freeListenStatus
				+ ", status" + status
				+ ", isPaidAlbum" + isPaidAlbum
				+ ", offlineType" + offlineType
				+ ", isPodcastAlbum" + isPodcastAlbum
				+ ", isChildVipAlbum" + isChildVipAlbum
				+ "]";
	}

	@Override
	public int describeContents()
	{
		return 0;
	}

	@Override
	public void writeToParcel(Parcel arg0, int arg1)
	{
		arg0.writeLong(this.albumId);
		arg0.writeString(this.albumTitle);
		arg0.writeString(this.coverUrlSmall);
		arg0.writeString(this.coverUrlMiddle);
		arg0.writeString(this.coverUrlLarge);
		arg0.writeString(this.recSrc);
		arg0.writeString(this.recTrack);
		arg0.writeInt(this.serializeStatus);
		arg0.writeInt(preferredType);
		arg0.writeInt(vipFreeType);
		arg0.writeInt(isVipFree ? 1 : 0);
		arg0.writeInt(isPaid ? 1 : 0);
		arg0.writeString(albumSubscript);
		arg0.writeString(coverLarge);
		arg0.writeLong(welistenAlbumId);
		arg0.writeInt(freeListenStatus);
		arg0.writeInt(type);
		arg0.writeInt(status);
		arg0.writeInt(isPaidAlbum ? 1 : 0);
		arg0.writeInt(offlineType);
		arg0.writeInt(isPodcastAlbum ? 1 : 0);
		arg0.writeInt(isChildVipAlbum ? 1 : 0);
	}

	public void readFromParcel(Parcel source)
	{
		this.albumId = source.readLong();
		this.albumTitle = source.readString();
		this.coverUrlSmall = source.readString();
		this.coverUrlMiddle = source.readString();
		this.coverUrlLarge = source.readString();
		this.recSrc = source.readString();
		this.recTrack = source.readString();
		this.serializeStatus = source.readInt();
		this.preferredType = source.readInt();
		this.vipFreeType = source.readInt();
		this.isVipFree = source.readInt() == 1 ? true : false;
		this.isPaid = source.readInt() == 1 ? true : false;
		this.albumSubscript = source.readString();
		this.coverLarge = source.readString();
		this.welistenAlbumId = source.readLong();
		this.freeListenStatus = source.readInt();
		this.type = source.readInt();
		this.status = source.readInt();
		this.isPaidAlbum = source.readInt() == 1;
		this.offlineType = source.readInt();
		this.isPodcastAlbum = source.readInt() == 1;
		this.isChildVipAlbum = source.readInt() == 1;
	}

	public static final Creator<SubordinatedAlbum> CREATOR = new Creator<SubordinatedAlbum>()
	{

		@Override
		public SubordinatedAlbum[] newArray(int size)
		{
			return new SubordinatedAlbum[size];
		}

		@Override
		public SubordinatedAlbum createFromParcel(Parcel source)
		{
			SubordinatedAlbum info = new SubordinatedAlbum();
			info.readFromParcel(source);
			return info;
		}
	};

	/**
	 * 返回一个可用的封面图
	 *
	 * @return
	 */
	public String getValidCover() {
		if (!TextUtils.isEmpty(coverUrlMiddle) && !coverUrlMiddle.equals("null"))
			return coverUrlMiddle;
		if (!TextUtils.isEmpty(coverUrlSmall) && !coverUrlSmall.equals("null"))
			return coverUrlSmall;
		if (!TextUtils.isEmpty(coverUrlLarge) && !coverUrlLarge.equals("null"))
			return coverUrlLarge;
		return "";
	}

	public boolean supportFreeListen() {
		return freeListenStatus == 1;
	}

	public boolean isSketchVideoAlbum() {
		return type == 32;
	}
}
