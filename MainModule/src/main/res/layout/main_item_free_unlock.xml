<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="65dp"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/main_bg_free_unlock_first"
    android:paddingLeft="12dp"
    android:paddingRight="12dp">

    <TextView
        android:id="@+id/main_tv_notice"
        android:layout_width="wrap_content"
        android:maxWidth="220dp"
        android:layout_height="20dp"
        android:gravity="bottom"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="13小时后 可免费解锁本集13小时后 可免费解锁本集13小时后 可免费解锁本集"
        android:textColor="#333333"
        android:textSize="15dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:textStyle="bold"
        android:layout_marginTop="11dp"
        android:layout_marginRight="20dp"/>


    <ImageView
        android:id="@+id/main_iv_icon_free"
        android:layout_width="37dp"
        android:layout_height="17dp"
        android:src="@drawable/main_icon_free_unlock_activity"
        app:layout_constraintTop_toTopOf="@+id/main_tv_notice"
        app:layout_constraintLeft_toRightOf="@+id/main_tv_notice"
        app:layout_constraintBottom_toBottomOf="@+id/main_tv_notice"
        android:layout_marginLeft="5dp"
        tools:visibility="visible"
        />

    <TextView
        android:id="@+id/main_tv_to_unlock"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/main_bg_free_unlock_btn"
        android:text="立即解锁"
        android:paddingTop="6dp"
        android:paddingBottom="6dp"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        android:textSize="13dp"
        android:textStyle="bold"
        android:textColor="@color/main_white"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <TextView
        android:id="@+id/main_tv_unlock_subtip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="观看完毕后获得24小时收听权限观看完毕后获得24小时收听权限观看完毕后获得24小时收听权限观看完毕后获得24小时收听权限"
        android:textColor="@color/main_color_999999_8d8d91"
        android:textSize="12dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:maxLength="27"
        android:includeFontPadding="false"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="13.5dp"
        android:layout_marginTop="4dp" />


</androidx.constraintlayout.widget.ConstraintLayout>