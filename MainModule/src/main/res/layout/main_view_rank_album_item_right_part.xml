<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:showIn="@layout/main_item_rank_album_list">

    <com.ximalaya.ting.android.framework.view.image.RoundImageView
        android:id="@+id/main_iv_cover"
        android:layout_width="65dp"
        android:layout_height="65dp"
        android:layout_marginTop="14dp"
        android:layout_marginBottom="13dp"
        app:corner_radius="4dp"
        android:contentDescription="@string/main_album_cover"
        tools:src="@drawable/host_default_album"
        />

    <com.ximalaya.ting.android.framework.view.image.FlexibleRoundImageView
        android:id="@+id/main_iv_album_cover_tag"
        style="@style/main_album_cover_tag_size_small"
        android:layout_alignLeft="@+id/main_iv_cover"
        app:flexible_round_corner="left_top"
        app:flexible_round_corner_radius="4dp"
        android:layout_alignTop="@+id/main_iv_cover"
        tools:src="@drawable/host_album_tag_pay"
        android:visibility="invisible"
        tools:visibility="visible"/>

    <ImageView
        android:id="@+id/main_iv_activity_tag"
        android:layout_width="65dp"
        android:layout_height="wrap_content"
        android:layout_alignLeft="@id/main_iv_cover"
        android:layout_alignRight="@id/main_iv_cover"
        android:layout_alignBottom="@id/main_iv_cover"
        android:contentDescription="@string/main_activity_tag"
        android:scaleType="fitStart"
        android:visibility="invisible"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/main_ll_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="65dp"
        android:layout_toEndOf="@+id/main_iv_cover"
        android:layout_alignTop="@+id/main_iv_cover"
        android:layout_toStartOf="@+id/main_ll_score"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="15dp"
        android:layout_marginBottom="8dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/main_tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxLines="2"
            android:ellipsize="end"
            android:textColor="@color/main_color_333333_888888"
            android:textSize="14sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-light"
            android:includeFontPadding="false"
            tools:text="东宫（匪我思存经典古言）"/>

        <TextView
            android:id="@+id/main_tv_sub_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:ellipsize="end"
            android:layout_marginTop="2dp"
            android:textColor="@color/main_color_999999_888888"
            android:textSize="11sp"
            android:includeFontPadding="false"
            tools:text="再现盛世王朝的爱情记忆"
            />

        <Space
            android:id="@+id/main_album_space"
            android:layout_width="match_parent"
            android:layout_weight="1"
            android:layout_height="0dp"
            android:visibility="visible"/>

        <TextView
            android:id="@+id/main_tv_hot"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:ellipsize="end"
            android:layout_marginTop="6dp"
            android:textColor="@color/main_color_bbbbbb_888888"
            android:textSize="10sp"
            android:includeFontPadding="false"
            tools:text="热度：1028" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="65dp"
        android:layout_marginEnd="12dp"
        android:layout_alignTop="@+id/main_iv_cover"
        android:layout_alignParentEnd="true"
        android:id="@+id/main_ll_score"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone">

        <TextView
            android:id="@+id/main_tv_score"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textColor="@color/host_color_f86442"
            tools:text="9"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="10sp"
            android:textColor="@color/main_color_999999_888888"
            android:text="@string/main_score"/>

    </LinearLayout>

    <View
        android:id="@+id/main_v_divider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_alignLeft="@id/main_iv_cover"
        android:layout_alignParentBottom="true"
        android:background="@color/main_color_eeeeee_2a2a2a"
        />


</RelativeLayout>