<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <FrameLayout
        android:id="@+id/main_fl_player_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@null"
        app:layout_constraintDimensionRatio="1.875"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/main_iv_whole_album_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@null"
            android:contentDescription="@string/main_iv_cd_album_image"
            android:scaleType="centerCrop"
            android:visibility="invisible"
            tools:src="@drawable/host_default_focus_img"
            tools:visibility="visible" />


        <ImageView
            android:id="@+id/main_iv_video_play_btn"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_gravity="center"
            android:alpha="0.5"
            android:contentDescription="视频播放"
            android:src="@drawable/host_album_video_play_big_ic"
            android:visibility="invisible"
            tools:visibility="visible" />
    </FrameLayout>

    <View
        android:id="@+id/main_whole_album_cover_bottom_shadow"
        android:layout_width="0dp"
        android:layout_height="60dp"
        android:background="@drawable/main_bg_40000_0000"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/main_fl_player_container"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:visibility="visible" />

    <FrameLayout
        android:id="@+id/main_album_cover_small_group"
        android:layout_width="136dp"
        android:layout_height="136dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/main_tv_play_count"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <View
            android:id="@+id/main_album_single_album_cover_shadow"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/main_album_single_album_cover_shadow"
            android:visibility="visible" />

        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:id="@+id/main_album_cover_small"
            android:layout_width="98dp"
            android:layout_height="98dp"
            android:layout_gravity="center"
            android:visibility="visible"
            app:corner_radius="6dp"
            tools:visibility="visible" />

    </FrameLayout>

    <com.ximalaya.ting.android.main.view.RoundOverlyingRecyclerView
        android:id="@+id/main_round_images_recycler_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintBottom_toBottomOf="@id/main_fl_player_container"
        app:layout_constraintLeft_toLeftOf="parent"
        tools:layout_height="20dp"
        tools:layout_width="48dp" />

    <TextView
        android:id="@+id/main_tv_play_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="7dp"
        android:fontFamily="PingFangSC-Regular"
        android:gravity="center_vertical"
        android:textColor="@color/main_white"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@id/main_round_images_recycler_view"
        app:layout_constraintLeft_toRightOf="@id/main_round_images_recycler_view"
        app:layout_constraintTop_toTopOf="@id/main_round_images_recycler_view"
        tools:text="879.8万播放879.8万播放" />

    <TextView
        android:id="@+id/main_album_whole_service_refund"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="15dp"
        android:background="@drawable/main_album_whole_support_serivce_refund_bg"
        android:drawableLeft="@drawable/main_money_rmb_green"
        android:drawablePadding="3dp"
        android:gravity="center_vertical"
        android:paddingRight="6dp"
        android:paddingBottom="1dp"
        android:text="7天无忧退"
        android:textColor="@color/main_white"
        android:textSize="12sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/main_tv_play_count"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/main_tv_play_count"
        tools:visibility="visible" />

    <!-- 条目活动图 -->
    <RelativeLayout
        android:id="@+id/main_item_ad_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/main_fl_player_container"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/main_iv_item_ad"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_centerVertical="true"
            android:contentDescription="@string/main_iv_cd_ad_icon"
            android:scaleType="fitXY" />

        <ImageView
            android:id="@+id/main_item_ad_tag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignRight="@id/main_iv_item_ad"
            android:layout_alignBottom="@id/main_iv_item_ad"
            android:visibility="gone" />
    </RelativeLayout>

    <include
        android:id="@+id/main_area_price"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="3dp"
        layout="@layout/main_view_whole_album_price"
        app:layout_constraintTop_toBottomOf="@id/main_item_ad_layout"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <RelativeLayout
        android:id="@+id/main_album_title_area"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="3dp"
        android:layout_marginRight="16dp"
        android:minHeight="37dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/main_iv_subscribe_area"
        app:layout_constraintTop_toBottomOf="@id/main_area_price">

        <TextView
            android:id="@+id/main_album_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="left|center_vertical"
            android:maxLines="2"
            android:textColor="@color/main_color_black"
            android:textSize="18sp"
            android:textStyle="bold"
            tools:text="每天听见吴晓波每天听见吴晓波每天听见吴晓波每天听见吴晓波每天听见吴晓波每天听见吴" />

        <ImageView
            android:id="@+id/main_album_title_icon"
            android:layout_width="wrap_content"
            android:layout_height="18dp"
            android:scaleType="fitXY"
            android:visibility="gone"
            android:layout_marginTop="4dp"
            android:layout_alignParentTop="true"
            android:layout_alignParentLeft="true"
            tools:visibility="visible" />

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/main_iv_subscribe_area"
        android:layout_width="32dp"
        android:layout_height="37dp"
        android:orientation="vertical"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:gravity="center"
        app:layout_constraintTop_toTopOf="@id/main_album_title_area"
        app:layout_constraintRight_toRightOf="parent">

        <ImageView
            android:id="@+id/main_iv_subscribe"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:contentDescription="订阅，按钮"
            android:src="@drawable/main_ic_album_subscribe"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/main_iv_subscribe_text"
            android:layout_width="wrap_content"
            android:layout_height="12dp"
            android:text="订阅"
            android:textSize="8sp"
            android:gravity="center"
            android:layout_marginTop="-5dp"
            android:textColor="@color/main_color_666666_999999" />

    </LinearLayout>

    <TextView
        android:id="@+id/main_album_subtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:gravity="left"
        android:lineSpacingExtra="2dp"
        android:textColor="@color/main_color_777777_888888"
        android:textSize="12sp"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:padding="5dp"
        android:background="@drawable/main_bg_rect_whole_album_sub_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/main_album_title_area"
        tools:text="这些世界上正在发生的事情，关系着你钱包的厚薄这些世界上正在发生的事情，关系着你钱包的厚薄这些世界上正在发生的事情，关系着你钱包的厚薄。" />

    <TextView
        android:id="@+id/main_album_activity_tip"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textSize="11sp"
        android:textColor="@color/main_color_FD3622"
        android:paddingTop="5dp"
        app:layout_constraintLeft_toLeftOf="@id/main_album_title_area"
        app:layout_constraintRight_toRightOf="@id/main_album_title_area"
        app:layout_constraintTop_toBottomOf="@id/main_album_subtitle"
        tools:text="423 知识好物节·特别攻略为您推荐 前往查看>" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/main_trainingCamp_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="main_trainingCamp_title,main_trainingCamp_content,main_trainingCamp_arrow"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/main_trainingCamp_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp"
        android:background="@drawable/main_rect_round4_border_ff5028"
        android:paddingLeft="5dp"
        android:paddingTop="2dp"
        android:paddingRight="5dp"
        android:paddingBottom="2dp"
        android:text="训练营服务"
        android:textColor="#FFFF5028"
        android:textSize="14sp"
        app:layout_constraintLeft_toLeftOf="@id/main_album_title_area"
        app:layout_constraintTop_toBottomOf="@id/main_album_activity_tip" />

    <TextView
        android:id="@+id/main_trainingCamp_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="16dp"
        android:paddingLeft="13dp"
        android:paddingRight="15dp"
        android:singleLine="true"
        android:textColor="@color/main_color_999999_888888"
        android:textSize="14sp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/main_trainingCamp_title"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintLeft_toRightOf="@id/main_trainingCamp_title"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/main_trainingCamp_title"
        tools:text="老师答疑 | 作业批改 | 同学讨论" />

    <ImageView
        android:id="@+id/main_trainingCamp_arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/host_arrow_gray_right"
        app:layout_constraintBottom_toBottomOf="@id/main_trainingCamp_content"
        app:layout_constraintRight_toRightOf="@id/main_trainingCamp_content"
        app:layout_constraintTop_toTopOf="@id/main_trainingCamp_content" />

    <View
        android:id="@+id/main_transparent_divider"
        android:layout_width="0dp"
        android:layout_height="10dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/main_trainingCamp_title" />

    <LinearLayout
        android:id="@+id/main_whole_album_below_album_cover_bar_root"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/main_transparent_divider" />

    <View
        android:id="@+id/main_border3"
        android:layout_width="0dp"
        android:layout_height="5dp"
        android:background="@color/framework_bg_color"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/main_whole_album_below_album_cover_bar_root"
        tools:visibility="visible" />

    <ViewStub
        android:id="@+id/main_stub_off_sale"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:inflatedId="@+id/main_off_sale"
        android:layout="@layout/main_layout_album_off_sale_new"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/main_border3" />

</androidx.constraintlayout.widget.ConstraintLayout>