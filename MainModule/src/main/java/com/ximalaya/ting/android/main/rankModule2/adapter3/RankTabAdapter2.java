package com.ximalaya.ting.android.main.rankModule2.adapter3;

import android.graphics.Typeface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.rankModule.model.RankNew2;
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil;
import com.ximalaya.ting.android.xmtrace.widget.AbRecyclerViewAdapter;

import java.util.List;

public class RankTabAdapter2 extends AbRecyclerViewAdapter<RankTabAdapter2.RankTabViewHolder> {

    private List<RankNew2> mRankList;
    private int mCurrentSelectedPosition;
    private IOnTabSelectedListener mOnTabSelectedListener;
    private BaseFragment2 mFragment;

    public RankTabAdapter2(List<RankNew2> rankList, IOnTabSelectedListener onTabSelectedListener, BaseFragment2 fragment2) {
        mRankList = rankList;
        mOnTabSelectedListener = onTabSelectedListener;
        mCurrentSelectedPosition = 0;
        mFragment = fragment2;
    }

    @Override
    public Object getItem(int i) {
        if (mRankList != null && i >= 0 && i < mRankList.size()) {
            return mRankList.get(i);
        }
        return null;
    }

    @NonNull
    @Override
    public RankTabViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.main_item_rank_tab2, parent, false);
        return new RankTabViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull final RankTabViewHolder holder, int position) {
        if (mRankList != null && position >= 0 && position < mRankList.size()) {
            final RankNew2 rankNew = mRankList.get(position);
            if (holder.tvItem != null && rankNew != null) {
                holder.tvItem.setText(rankNew.getName());
                holder.tvItem.setSelected(mCurrentSelectedPosition == position);
                if (holder.tvItem.isSelected()) {
                    ViewStatusUtil.setVisible(View.VISIBLE, holder.indicatorView);
                    holder.tvItem.setTypeface(Typeface.create("sans-serif-light", Typeface.BOLD));
                    holder.tvItem.setTextSize(15);
                } else {
                    ViewStatusUtil.setVisible(View.INVISIBLE, holder.indicatorView);
                    holder.tvItem.setTypeface(Typeface.create("", Typeface.NORMAL));
                    holder.tvItem.setTextSize(13);
                }
                holder.tvItem.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        mCurrentSelectedPosition = holder.getAdapterPosition();
                        notifyDataSetChanged();
                        if (mOnTabSelectedListener != null) {
                            mOnTabSelectedListener.onTabSelected(rankNew, position);
                        }
                        // 加了后反而导致无障碍焦点错乱
//                        if (mFragment != null) {
//                            mFragment.postOnUiThread(() -> v.sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_HOVER_ENTER));
//                        }
                    }
                });
            }
        }
    }

    @Override
    public int getItemCount() {
        if (mRankList != null) {
            return mRankList.size();
        }
        return 0;
    }

    public int getCurrentSelectedPosition() {
        return mCurrentSelectedPosition;
    }

    public void setCurrentSelectedPosition(int currentSelectedPosition) {
        mCurrentSelectedPosition = currentSelectedPosition;
    }

    static class RankTabViewHolder extends RecyclerView.ViewHolder {
        TextView tvItem;
        View indicatorView;

        RankTabViewHolder(View itemView) {
            super(itemView);
            tvItem = itemView.findViewById(R.id.main_tab_tv);
            indicatorView = itemView.findViewById(R.id.main_indicator);
        }
    }

    public interface IOnTabSelectedListener {
        void onTabSelected(RankNew2 rankNew, int pos);
    }
}
