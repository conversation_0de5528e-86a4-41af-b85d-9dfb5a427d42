package com.ximalaya.ting.android.main.anchorModule.v5.works

import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Typeface
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.widget.TextViewCompat
import com.ximalaya.ting.android.host.adapter.brv4.viewholder.QuickViewHolder
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.anchorModule.v5.AnchorCardData
import com.ximalaya.ting.android.main.anchorModule.v5.AnchorSpaceViewModel
import com.ximalaya.ting.android.main.anchorModule.v5.util.AnchorTraceUtil
import com.ximalaya.ting.android.main.mine.extension.visible

/**
 * author : mingming.li
 * email : <EMAIL>
 * date : 2024/8/14
 * 全部作品竖排、相关作品
 */
class AnchorRelativeWorksProvider(fragment: AnchorSpaceWorksTabFragment) :
    BaseItemTypeProvider(fragment) {
    override val itemViewType: Int
        get() = AnchorCardData.TYPE_RELATE_WORK_TITLE

    override fun onCreate(context: Context, parent: ViewGroup, viewType: Int): QuickViewHolder {
        return QuickViewHolder(
            R.layout.main_layout_anchor_provider_relative_work_title, parent
        )
    }

    override fun onBind(holder: QuickViewHolder, position: Int, item: AnchorCardData?) {
        super.onBind(holder, position, item)
        item ?: return
        val countStr = with(item.header?.count?.anchorRelatedAlbumCount ?: 0) {
            if (this > 0) toString() else ""
        }
        // 标题和数量(主创+参演)
        val tvCount = holder.getView<TextView>(R.id.main_tv_model_count)
        val tvTitleFilter = holder.getView<TextView>(R.id.main_tv_filter)
        val tvTitle = holder.getView<TextView>(R.id.main_tv_model_title)
        val cslFilter = holder.getView<ConstraintLayout>(R.id.main_csl_filter)

        tvTitle.text = item.header?.title
        tvCount.text = countStr
        cslFilter.visible(if (getViewModel()?.stateIsFiltering?.value == true) View.VISIBLE else View.GONE)
        tvTitleFilter.apply {
            isSelected = getViewModel()?.stateIsFiltering?.value == true
            updateFilterTextView(this)
            setOnClickListener {
                AnchorTraceUtil.traceClickPopFilter(item.header?.title ?: "")
                it.isSelected = !it.isSelected
                getViewModel()?.stateIsFiltering?.value = it.isSelected
                updateFilterTextView(it as TextView)
                cslFilter.visible(if (it.isSelected) View.VISIBLE else View.GONE)
            }
        }

        with(listOf(
            R.id.main_mb_play_most,
            R.id.main_mb_recent_update,
        ).map { id ->
            holder.getView<TextView>(id)
        }) {
            val orderBys =
                listOf(AnchorSpaceViewModel.KEY_TYPE_HOT, AnchorSpaceViewModel.KEY_TYPE_NEW)
            forEachIndexed { index, tv ->
                tv.setOnClickListener {
                    if (it.isSelected) return@setOnClickListener
                    forEach { tv ->
                        tv.isSelected = false
                        tv.typeface = Typeface.DEFAULT
                    }
                    it.isSelected = true
                    (it as TextView).typeface = Typeface.DEFAULT_BOLD
                    getViewModel()?.apply {
                        orderBy = orderBys[index]
                        filterAnchorWorksData(item.cardType)
                    }
                }
                if (getViewModel()?.orderBy == orderBys[index]) {
                    forEach {
                        it.isSelected = false
                        it.typeface = Typeface.DEFAULT
                    }
                    tv.isSelected = true
                    tv.typeface = Typeface.DEFAULT_BOLD
                }
            }
        }

        with(listOf(
            R.id.main_mb_publish_all,
            R.id.main_mb_publish_finish,
            R.id.main_mb_publishing
        ).map { id ->
            holder.getView<TextView>(id)
        }) {
            val publish = listOf(
                AnchorSpaceViewModel.FINISH_ALL,
                AnchorSpaceViewModel.FINISH_FINISHED,
                AnchorSpaceViewModel.FINISH_UNFINISHED
            )
            forEachIndexed { index, tv ->
                tv.setOnClickListener {
                    if (it.isSelected) return@setOnClickListener
                    forEach { tv ->
                        tv.isSelected = false
                        tv.typeface = Typeface.DEFAULT
                    }
                    it.isSelected = true
                    (it as TextView).typeface = Typeface.DEFAULT_BOLD
                    getViewModel()?.apply {
                        finishStatus = publish[index]
                        filterAnchorWorksData(item.cardType)
                    }
                }
                if (getViewModel()?.finishStatus == publish[index]) {
                    forEach {
                        it.isSelected = false
                        it.typeface = Typeface.DEFAULT
                    }
                    tv.isSelected = true
                    tv.typeface = Typeface.DEFAULT_BOLD
                }
            }
        }

        with(listOf(
            R.id.main_mb_price_all,
            R.id.main_mb_price_free,
            R.id.main_mb_price_paid,
        ).map { id ->
            holder.getView<TextView>(id)
        }) {
            val paid = listOf(
                AnchorSpaceViewModel.PAID_ALL,
                AnchorSpaceViewModel.PAID_FREE,
                AnchorSpaceViewModel.PAID_PAID,
            )
            forEachIndexed { index, tv ->
                tv.setOnClickListener {
                    if (it.isSelected) return@setOnClickListener
                    forEach { tv ->
                        tv.isSelected = false
                        tv.typeface = Typeface.DEFAULT
                    }
                    it.isSelected = true
                    (it as TextView).typeface = Typeface.DEFAULT_BOLD
                    getViewModel()?.apply {
                        paidType = paid[index]
                        filterAnchorWorksData(item.cardType)
                    }
                }
                if (getViewModel()?.paidType == paid[index]) {
                    forEach {
                        it.isSelected = false
                        it.typeface = Typeface.DEFAULT
                    }
                    tv.isSelected = true
                    tv.typeface = Typeface.DEFAULT_BOLD
                }
            }
        }
    }

    private fun updateFilterTextView(it: TextView) {
        val color = ContextCompat.getColor(
            it.context,
            if (it.isSelected) R.color.host_color_xmRed else R.color.host_color_titleColor
        )
        it.setTextColor(color)
        it.setCompoundDrawablesWithIntrinsicBounds(
            0, 0, if (it.isSelected) {
                R.drawable.main_ic_anchor_filter_up
            } else {
                R.drawable.main_ic_anchor_filter_down
            }, 0
        )
        TextViewCompat.setCompoundDrawableTintList(it, ColorStateList.valueOf(color))
    }
}