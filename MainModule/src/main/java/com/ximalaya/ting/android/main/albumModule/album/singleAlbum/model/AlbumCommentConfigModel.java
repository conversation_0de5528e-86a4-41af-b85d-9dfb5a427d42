package com.ximalaya.ting.android.main.albumModule.album.singleAlbum.model;

import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.host.model.album.AlbumCommentModel;

import java.util.List;

public class AlbumCommentConfigModel {

    @SerializedName("isUpdate")
    public boolean isUpdate;
    @SerializedName("isPaidAlbum")
    public boolean isPaidAlbum;
    @SerializedName("recStatus")
    public int recStatus;
    @SerializedName("comment")
    public AlbumCommentModel comment;
    @SerializedName("scoreControl")
    public ScoreControlModel scoreControl;
    @SerializedName("playDuration")
    public int playDuration;   //播放时长(分钟)
    @SerializedName("userWithPaidType")
    public int userWithPaidType;
    @SerializedName("extraScoreBindings")
    public List<ExtraScoreConfigModel> extraScoreBindings;

    @SerializedName("albumTitle")
    public String albumTitle;

    public static class ScoreControlModel {
        @SerializedName("allowScore")
        public boolean allowScore;
        @SerializedName("tip")
        public String tip;
    }

    public static class ExtraScoreConfigModel {
        @SerializedName("type")
        public int type;
        @SerializedName("name")
        public String name;
    }
}
