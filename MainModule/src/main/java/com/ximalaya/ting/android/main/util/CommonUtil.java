package com.ximalaya.ting.android.main.util;

import android.Manifest;
import android.app.Activity;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.style.ClickableSpan;
import android.view.View;
import android.widget.TextView;

import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.model.anchor.Anchor;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 一些通用的小功能
 *
 * <AUTHOR>
 */
public final class CommonUtil {

    private CommonUtil() {
    }

    /**
     * 根据数据项获得一个有限长度的字符串 如：Lucy、Tomas、Louis、Chri...... 完整显示若干项，最后一项以...收尾 可以提供每一项的点击事件
     *
     * @param users 用户列表（考虑换成String更通用）
     * @param max 最大字符长度
     * @param suffix 后缀：如"也收听了"
     * @param listener 点击监听
     * @param hasLink 是否有链接
     * @return Data（包含结果字符串和一个可见项数量）
     */
    public static Data getLimitedNicknameLine(List<Anchor> users, int max, String suffix, final IOnClick listener, boolean hasLink) {
        if (!TextUtils.isEmpty(suffix)) {
            for (int i = 0; i < suffix.length(); i++) {
                if (isChinese(suffix.charAt(i))) {
                    max -= 2;
                } else {
                    max--;
                }
            }
        }
        StringBuilder sb = new StringBuilder();
        int visible = 0;
        if (users != null && !users.isEmpty()) {
            int count = users.size();
            int len = 0;
            for (int i = 0; i < count; i++) {
                Anchor anchor = users.get(i);
                if (anchor == null || anchor.getNickName() == null) {
                    continue;
                }
                String name = anchor.getNickName();
                for (int j = 0; j < name.length(); j++) {
                    if (isChinese(name.charAt(j))) {
                        len += 2;
                    } else {
                        len++;
                    }
                }
                // 分隔符
                len += 2;
                visible++;
                if (len >= max) {
                    break;
                }
            }

            int left = max;
            for (int i = 0; i < visible; i++) {
                Anchor anchor = users.get(i);
                if (anchor == null || anchor.getNickName() == null) {
                    continue;
                }
                String name = anchor.getNickName();
                int nameLen = 0;
                boolean out = false;
                for (int j = 0; j < name.length(); j++) {
                    if (isChinese(name.charAt(j))) {
                        nameLen += 2;
                    } else {
                        nameLen++;
                    }
                    if (nameLen > left) {
                        name = name.substring(0, j) + "...";
                        anchor.setNickName(name);
                        sb.append(name);
                        out = true;
                        break;
                    }
                }
                if (out) {
                    break;
                }
                sb.append(name);
                if (i != visible - 1) {
                    sb.append("、");
                    left -= 2;
                }
                left -= nameLen;
            }
        }
        sb.append(" ");
        sb.append(suffix);

        SpannableString spannable = new SpannableString(sb.toString());

        if (hasLink) {
            int pos = 0;
            if (users != null && !users.isEmpty()) {
                for (int i = 0; i < visible; i++) {
                    final Anchor anchor = users.get(i);
                    if (anchor == null || anchor.getNickName() == null) {
                        continue;
                    }
                    String name = anchor.getNickName();
                    int start = pos;
                    int end = pos += name.length();
                    pos += 1;
                    ClickableSpan clickableSpan = new ClickableSpan() {
                        @Override
                        public void onClick(View widget) {
                            if (listener != null) {
                                listener.onClick(anchor);
                            }
                        }

                        @Override
                        public void updateDrawState(TextPaint ds) {
                            super.updateDrawState(ds);
                            ds.setColor(0xFF4A90E2);
                            ds.setUnderlineText(false);
                        }
                    };
                    spannable.setSpan(clickableSpan, start, end, Spanned.SPAN_INCLUSIVE_INCLUSIVE);
                }
            }
        }

        Data data = new Data();
        data.visibleCount = visible;
        data.string = spannable;
        return data;
    }

    public static boolean isChinese(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        return ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
            || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A
            || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B
            || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
            || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS_SUPPLEMENT;
    }

    //数字转汉字表示
    public static String int2ChineseNum(int src) {
        final String[] num = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
        final String[] unit = {"", "十", "百", "千", "万", "十", "百", "千", "亿", "十", "百", "千"};
        StringBuilder dst = new StringBuilder();
        int count = 0;
        while (src > 0) {
            dst.insert(0, num[src % 10] + unit[count]);
            src = src / 10;
            count++;
        }
        return dst.toString().replaceAll("零[千百十]", "零").replaceAll("零+万", "万")
            .replaceAll("零+亿", "亿").replaceAll("亿万", "亿零")
            .replaceAll("零+", "零").replaceAll("^一十", "十").replaceAll("零$", "");
    }

    public static void setCount(TextView view, long count) {
        setCount(view, count, "");
    }

    public static void setCount(TextView view, long count, String defaultMsg) {
        setCount(view, count, 999, defaultMsg);
    }

    /**
     * 显示数量（评论、喜欢）
     *
     * @param view 目标TextView
     * @param count 数量
     * @param limit 显示上限
     * @param defaultMsg 默认文本
     */
    public static void setCount(TextView view, long count, long limit, String defaultMsg) {
        if (view == null) {
            return;
        }

        if (count > 0) {
            if (count > limit) {
                view.setText(limit + "+");
            } else {
                view.setText(String.valueOf(count));
            }
        } else {
            view.setText(defaultMsg == null ? "" : defaultMsg);
        }
    }

    public static boolean isAnotherDay(long time) {
        if (time == 0) {
            return true;
        }

        Calendar now = Calendar.getInstance();
        Calendar shareC = Calendar.getInstance();
        shareC.setTime(new Date(time));

        if (now.get(Calendar.YEAR) != shareC.get(Calendar.YEAR)) {
            return true;
        }

        if (now.get(Calendar.MONTH) != shareC.get(Calendar.MONTH)) {
            return true;
        }

        if (now.get(Calendar.DATE) != shareC.get(Calendar.DATE)) {
            return true;
        }

        Logger.i("CommonUtil", "不是新的一天");
        return false;
    }


    /**
     * 英文、数字占一个字符 其余占两个字符
     * */
    public static int getCurLength(CharSequence str) {
        int length = 0;
        if (TextUtils.isEmpty(str)) {
            return length;
        } else {
            for (int i = 0; i < str.length(); i++) {
                if (str.charAt(i) < 128) {
                    length += 1;
                } else {
                    length += 2;
                }
            }
        }
        return length;
    }

    public static <T> T safelyGetItem(List<T> list, int index) {
        if (0 > index || null == list) {
            return null;
        }
        if (index >= list.size()) {
            return null;
        }
        return list.get(index);
    }


    public interface IOnClick {

        void onClick(Anchor anchor);
    }

    public static class Data {

        public int visibleCount;
        public SpannableString string;
    }
}
