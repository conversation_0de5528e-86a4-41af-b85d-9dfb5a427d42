package com.ximalaya.ting.android.main.albumModule.album.album3;

import android.content.Context;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffColorFilter;
import android.graphics.drawable.Drawable;
import android.text.TextPaint;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Pair;
import android.util.TypedValue;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.airbnb.lottie.LottieComposition;
import com.airbnb.lottie.LottieCompositionFactory;
import com.airbnb.lottie.LottieDrawable;
import com.airbnb.lottie.LottieListener;
import com.airbnb.lottie.LottieProperty;
import com.airbnb.lottie.model.KeyPath;
import com.airbnb.lottie.value.LottieValueCallback;
import com.ximalaya.ting.android.downloadservice.base.BaseDownloadTask;
import com.ximalaya.ting.android.downloadservice.base.IDownloadStatus;
import com.ximalaya.ting.android.downloadservice.base.IDownloadTaskCallback;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.adapter.track.base.AbstractAlbumTrackAdapter;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.play.XPlayPageRef;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.pay.ISingleAlbumPayResultListener;
import com.ximalaya.ting.android.host.manager.pay.PayManager;
import com.ximalaya.ting.android.host.manager.play.AdMakeVipLocalManager;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.payment.UniversalPayment;
import com.ximalaya.ting.android.host.model.payment.UniversalPaymentCommonFragmentFinishCallBack;
import com.ximalaya.ting.android.host.model.payment.UniversalPaymentUnLockCommonCallBack;
import com.ximalaya.ting.android.host.model.payment.behavior.BehaviorAction;
import com.ximalaya.ting.android.host.model.payment.behavior.CommonBehavior;
import com.ximalaya.ting.android.host.model.play.CopyrightExtendInfo;
import com.ximalaya.ting.android.host.model.recommend.ShowTag;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.android.host.util.commercial.AlbumTypeUtil;
import com.ximalaya.ting.android.host.util.common.SpanUtils;
import com.ximalaya.ting.android.host.util.common.StringUtil;
import com.ximalaya.ting.android.host.util.common.TimeHelper;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.util.ui.AnimationUtil;
import com.ximalaya.ting.android.host.util.ui.TrackItemTagUtil;
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.host.view.XmLottieDrawable;
import com.ximalaya.ting.android.host.view.bar.RoundProgressBar;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.dialog.universal.RequestMaterial;
import com.ximalaya.ting.android.main.dialog.universal.UniversalPaymentActionsDialog;
import com.ximalaya.ting.android.main.manager.albumFragment.AlbumFragmentMarkPointManager;
import com.ximalaya.ting.android.main.manager.freelisten.IUnlockLogic;
import com.ximalaya.ting.android.main.payModule.BundleBuyDialogFragment;
import com.ximalaya.ting.android.main.payModule.BundleBuyDialogFragment1;
import com.ximalaya.ting.android.main.payModule.single.SingleAlbumPayManager;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.CommonTrackList;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * 付费声音条。支持付费业务。
 * 主播个人的专辑页面的声音条不可以用这个Adapter，而是用MyTrackAdapter。
 *
 * <AUTHOR> on 16/8/1.
 */
public class AlbumTrackAdapter3 extends AbstractAlbumTrackAdapter implements
        AdMakeVipLocalManager.IAlbumPageCallback, IUnlockLogic.IUnlockResultListener, IDownloadTaskCallback {

    private Track mCurBuyingTrack;//当前购买的声音
    private BundleBuyDialogFragment.IAlbumStatusChangedListener albumStatusChangedListener;
    private LottieDrawable mLottieDrawable;
    @Nullable
    private BaseFragment2 mFragment;
    @Nullable
    private CopyrightExtendInfo mCopyrightExtendInfo;


    private Drawable mPlayDrawable;
    private Drawable mTimeDrawable;
    private AlbumM mData;
    private int fromFragmentType = AlbumTypeUtil.TYPE_FROM_ALBUM_FRAGMENT;
    private BehaviorAction.IActionOuterImp actionOuterImp;
    private SingleAlbumPayManager singleAlbumPayManager;
    private ISingleAlbumPayResultListener payResultListener;


    public void setFromFragmentType(int fromFragmentType) {
        this.fromFragmentType = fromFragmentType;
    }


    public AlbumTrackAdapter3(Context activity, List<Track> listData, @Nullable BaseFragment2 fragment) {
        super(activity, listData);
        mFragment = fragment;
        mPlayDrawable = context.getResources().getDrawable(R.drawable.main_ic_podcast_play_count_tag).mutate();
        mPlayDrawable.setBounds(0, 0, BaseUtil.dp2px(context, 10), BaseUtil.dp2px(context, 10));

        mTimeDrawable = context.getResources().getDrawable(R.drawable.main_ic_podcast_time_amount_tag).mutate();
        mTimeDrawable.setBounds(0, 0, BaseUtil.dp2px(context, 12), BaseUtil.dp2px(context, 12));
        RouteServiceUtil.getDownloadService().registerDownloadCallback(this);
    }

    public void showPositionBright(int index) {
        brightPosition = index;
    }

    public void setPayResultListener(ISingleAlbumPayResultListener payResultListener) {
        this.payResultListener = payResultListener;
    }

    public void setAlbumStatusChangedListener(BundleBuyDialogFragment.IAlbumStatusChangedListener albumStatusChangedListener) {
        this.albumStatusChangedListener = albumStatusChangedListener;
    }

    public Track getCurBuyingTrack() {
        return mCurBuyingTrack;
    }

    public void onFragmentPause(ListView listView) {
        if (listView == null) {
            return;
        }
    }

    public void onFragmentResume(ListView listView) {
        if (listView == null) {
            return;
        }
    }

    @Override
    public void bindViewDatas(BaseViewHolder h, Track track, int position) {
        if (track == null || !(h instanceof ViewHolder)) {
            return;
        }
        ViewHolder holder = (ViewHolder) h;
        AlbumM album = null == mAlbumMDataProvider ? null : mAlbumMDataProvider.getAlbumM();
        if (track instanceof TrackM) {
            String num = String.valueOf(((TrackM) track).getOrderNo());
            adjustTvTextSize(holder.orderNo, BaseUtil.dp2px(context, 36), num);
            holder.orderNo.setText(num);
        }
        // 声音标签 播放进度设置
        bindPaidView(holder, track, position);
        // 10 + 36 + 16 + 20 + 6 + 16 = 104
        int showTagsWidth = BaseUtil.getScreenWidth(context) - BaseUtil.dp2px(context, 104);
        int lastPos = XmPlayerManager.getInstance(context).getHistoryPos(track.getDataId());
        Pair<Integer, String> ps = ShowNotesAlbumTrackAdapter3.getPlayScheduleForShowNotesNew(lastPos, track.getDuration());
        setUpTrackTags(holder, showTagsWidth, track, ps, position);

        // 声音标题和播放状态
        setTrackTitleAndPlayingStatus(holder, (ps != null && (ps.first == 3)), track, position);
        setClickListener(holder.moreBtn, track, position, holder);
        setClickListener(holder.lockIv, track, position, holder);
        setClickListener(holder.clickMask, track, position, holder);

        holder.title.setTag(R.id.main_sound_name, track);
        if (AlbumTypeUtil.TYPE_FROM_UNIVERSAL_CUSTOM_ALBUM == fromFragmentType) {
            AlbumFragmentMarkPointManager.Companion.markPointOnShowCustomedAlbumTrackList(mAlbumId, track.getXmRequestId(), track.getDataId(), album != null
                    && (album.isAuthorized() || AlbumTypeUtil.SingleAlbumRelative.isSingleAlbum(album) || AlbumTypeUtil.SingleAlbumRelative.isFreeAlbum(album)));
        } else {
            AlbumFragmentMarkPointManager.Companion.markPointOnShowAlbumTrackList(mAlbumId, mAlbumTitle, track.getUid(),
                    track.getCategoryId(), track.getDataId(), holder.root, track.getXmRequestId());
        }
    }

    private boolean bindPaidView(ViewHolder holder, Track track, int position) {
        AlbumM albumM = mAlbumMDataProvider != null ? mAlbumMDataProvider.getAlbumM() : null;

        boolean isLocked = false;
        if (!track.isAuthorized() && track.isPaid()) {
            if (track.getPriceTypeEnum() == PayManager.PAY_ALBUM_WHOLE
                    || track.getPriceTypeEnum() == PayManager.PAY_ALBUM_MEMBER
                    || track.getPriceTypeEnum() == PayManager.PAY_ALBUM_MEMBER_WHOLE) {
                isLocked = true;
            } else if (track.isFree()) {
                isLocked = false;
            } else {
                isLocked = true;
            }
        } else {
            isLocked = false;
        }
        if (isFreeListenAlbum) {
            //畅听专辑隐藏付费图标
            isLocked = false;
        }
        ViewStatusUtil.setVisible(isLocked ? View.VISIBLE : View.INVISIBLE, holder.lockIv);
        return isLocked;
    }

    private void setTrackTitleAndPlayingStatus(ViewHolder holder, boolean playCompleted, Track track, int position) {
        int space = 0;
        int titleColor = context.getResources().getColor(R.color.main_color_393942_dcdcdc);
        int orderNumberColor = Album3Utils.INSTANCE.getColorWithAlpha(0.8f, context.getResources().getColor(R.color.main_color_393942_dcdcdc));
        if (playCompleted) {
            orderNumberColor = titleColor = Album3Utils.INSTANCE.getColorWithAlpha(0.3f, context.getResources().getColor(R.color.main_color_393942_dcdcdc));
        }
        if (track instanceof TrackM) {
            // 处理播放中的动画
            boolean isPerformPlayingAnim = (PlayTools.isCurrentTrackPlaying(context, track) && brightPosition < 0) || brightPosition == position;
            if (isPerformPlayingAnim) {
                holder.playingFlag.setVisibility(View.VISIBLE);
                holder.lastListenTag.setVisibility(View.GONE);
                createPlayingLottieDrawableIfNotExist();
                XmPlayerManager xManager = XmPlayerManager.getInstance(context);
                titleColor = context.getResources().getColor(R.color.host_color_xmRed);
                orderNumberColor = Album3Utils.INSTANCE.getColorWithAlpha(0.8f, context.getResources().getColor(R.color.host_color_xmRed));
                // 广告过来的标识 不需要根据状态进行改变
                if (brightPosition >= 0) {
                    stopPlayingFlagLoading(holder.playingFlag);
                    holder.playingFlag.setImageDrawable(mLottieDrawable);
                    mLottieDrawable.cancelAnimation();
                } else if (!xManager.isPlaying()) {
                    if (xManager.isBuffering()) {
                        startPlayingFlagLoading(holder.playingFlag);
                    } else {
                        stopPlayingFlagLoading(holder.playingFlag);
                        holder.playingFlag.setImageDrawable(mLottieDrawable);
                        mLottieDrawable.cancelAnimation();
                    }
                } else {
                    stopPlayingFlagLoading(holder.playingFlag);
                    holder.playingFlag.setImageDrawable(mLottieDrawable);
                    holder.playingFlag.post(() -> mLottieDrawable.playAnimation());
                }
                space = BaseUtil.dp2px(context, 20);
            } else {
                stopPlayingFlagLoading(holder.playingFlag);
                if (holder.playingFlag.getDrawable() instanceof LottieDrawable) {
                    ((LottieDrawable) holder.playingFlag.getDrawable()).stop();
                    holder.playingFlag.setImageDrawable(null);
                }
                holder.playingFlag.setVisibility(View.GONE);
                if (lastPlayTrackId == track.getDataId() || PlayTools.isPlayCurrTrackById(context, track.getDataId())) {
                    holder.lastListenTag.setVisibility(View.VISIBLE);
                    if (!playCompleted) {
                        titleColor = context.getResources().getColor(R.color.main_color_393942_dcdcdc);
                        orderNumberColor = Album3Utils.INSTANCE.getColorWithAlpha(0.8f, context.getResources().getColor(R.color.main_color_393942_dcdcdc));
                    }
                    space = BaseUtil.dp2px(context, 28);
                } else {
                    holder.lastListenTag.setVisibility(View.GONE);
                }
            }
        }
        holder.orderNo.setTextColor(orderNumberColor);
        SpanUtils.with(holder.title)
                .appendSpace(space)
                .append(track.getTrackTitle())
                .setForegroundColor(titleColor)
                .create();
        setTrackDownloadStatus(context, holder.downloadStatusIv, RouteServiceUtil.getDownloadService().getDownloadStatus(track));
    }

    private void createPlayingLottieDrawableIfNotExist() {
        if (mLottieDrawable == null) {
            // 获取动画LottieDrawable
            mLottieDrawable = new XmLottieDrawable();
            String lottiePath = "lottie" + File.separator + "podcast_ic_playing.json";
            LottieListener<LottieComposition> listener = composition -> {
                mLottieDrawable.setComposition(composition);
                mLottieDrawable.setRepeatCount(LottieDrawable.INFINITE);
                int color = context.getResources().getColor(com.ximalaya.ting.android.host.R.color.host_color_xmRed);
                mLottieDrawable.addValueCallback(new KeyPath("**"), LottieProperty.COLOR_FILTER,
                        new LottieValueCallback<>(new PorterDuffColorFilter(color, PorterDuff.Mode.SRC_IN)));
            };
            LottieCompositionFactory.fromAsset(context, lottiePath).addListener(listener);
        }
    }

    private void setUpTrackTags(ViewHolder holder, int constraintWidth, Track track, Pair<Integer, String> pair, int position) {
        if (track instanceof TrackM) {
            int tagWidth = TrackItemTagUtil.INSTANCE.setTagsToViewForAlbum3(holder.tagTv, constraintWidth, ((TrackM) track).getLabelList());
            if (tagWidth > 0) {
                holder.tagTv.setVisibility(View.VISIBLE);
            } else {
                holder.tagTv.setVisibility(View.GONE);
            }
            int leftWidth = constraintWidth - tagWidth - BaseUtil.dp2px(context, 8);
            List<ShowTag> showTags = new ArrayList<>();
            ViewStatusUtil.setVisible(View.GONE, holder.progressBar);
            if (leftWidth > BaseUtil.dp2px(context, 20)) {
                if (pair.first == 3) {
                    showTags.add(new ShowTag(pair.second, AlbumTagsUtil.TYPE_COMPLETE, ""));
                } else if (pair.first == 2) {
                    leftWidth = leftWidth - BaseUtil.dp2px(context, 12);
                    double time = XmPlayerManager.getInstance(context).getHistoryPos(track.getDataId()) / (track.getDuration() * 1000d) * 100;
                    holder.progressBar.setProgress((int) time);
                    ViewStatusUtil.setVisible(View.VISIBLE, holder.progressBar);
                    showTags.add(new ShowTag("剩" + pair.second, AlbumTagsUtil.TYPE_OTHER, ""));
                } else {
                    showTags.add(new ShowTag(pair.second, AlbumTagsUtil.TYPE_DURATION, ""));
                }
                showTags.add(new ShowTag(getFriendlyTimeStr(track.getCreatedAt()), AlbumTagsUtil.TYPE_OTHER, ""));
                showTags.add(new ShowTag(StringUtil.getFriendlyNumStrNew(track.getPlayCount()), AlbumTagsUtil.TYPE_COUNT_PLAY, track.getPlayCount() + ""));
                boolean show = AlbumTagsUtil.INSTANCE.bindTagsView(holder.otherInfoLayout, showTags, leftWidth);
                if (!show) {
                    ViewStatusUtil.setVisible(View.GONE, holder.progressBar);
                }
            } else {
                holder.otherInfoLayout.setVisibility(View.GONE);
            }
        }
    }

    private void startPlayingFlagLoading(final ImageView ivPlayFlag) {
        Drawable leftDrawable = ViewStatusUtil.filterDrawable(context, R.drawable.main_album_ic_list_loading,
                context.getResources().getColor(com.ximalaya.ting.android.host.R.color.host_color_xmRed));
        ivPlayFlag.setImageDrawable(leftDrawable);
        AnimationUtil.rotateView(context, ivPlayFlag);
    }

    private void stopPlayingFlagLoading(ImageView ivPlayFlag) {
        AnimationUtil.stopAnimation(ivPlayFlag);
    }


    public void setData(AlbumM data) {
        mData = data;
    }

    @Override
    public void onClick(View view, Track track, int position, BaseViewHolder h) {
        if (!OneClickHelper.getInstance().onClick(view)) {
            return;
        }
        int i = view.getId();
        if (i == R.id.main_show_notes_play_layout) {
            // play
            if (track instanceof TrackM && mFragmentActionListener != null) {
                boolean isPlaying = PlayTools.isCurrentTrackPlaying(context, track);
                if (isPlaying) {
                    // 暂停
                    mFragmentActionListener.requestDoPlayClick(view, false);
                } else {
                    mFragmentActionListener.onMyItemClick(view, (TrackM) track, position, position);
                }
                AlbumFragmentMarkPointManager.Companion.markPointOnTrackListItemClick(mData, track,
                        "album", position, getTrackPlayStatus(track), "3", "音频");
            }
        } else if (i == R.id.main_iv_show_notes_more_btn) {
            if (mFragment instanceof AlbumFragmentNewList3) {
                ((AlbumFragmentNewList3) mFragment).showMoreDialog(track);
            }
        } else if (i == R.id.main_iv_lock) {
            AlbumFragmentMarkPointManager.Companion.markPointOnClickPriceIconInTrackList(track, position);
            showUniversalPaymentActionsDialog(track, position, true);
        } else if (i == R.id.main_album3_track_click_mask) {
            // 防误触遮罩
        }
    }

    public void showUniversalPaymentActionsDialog(Track track, int position, boolean isFromIcon) {
        CommonTrackList<TrackM> trackList = null == mAlbumMDataProvider ? null : (null == mAlbumMDataProvider.getAlbumM() ? null : mAlbumMDataProvider.getAlbumM().getCommonTrackList());
        RequestMaterial requestMaterial = new RequestMaterial(mAlbumId, UniversalPayment.SOURCE_ALBUM_TRACK_LIST)
                .setTrack(track).setActionOuterImp(getActionOuterImp()).setTrackList(trackList);
        if (isFromIcon) {
            requestMaterial.shutAutoPerform();
        }
        UniversalPaymentActionsDialog.requestAndShowDialog(mFragment, requestMaterial);
    }

    private BehaviorAction.IActionOuterImp getActionOuterImp() {
        if (null == actionOuterImp) {
            actionOuterImp = new BehaviorAction.IActionOuterImp() {
                @Override
                public boolean doOnTrackPurchase(Track track, BehaviorAction action) {
                    actionBuyTrack(track, false);
                    return true;
                }

                @Override
                public boolean doOnPurchaseForPurchase(CommonBehavior behavior, Track track, BehaviorAction action) {
                    UniversalPaymentCommonFragmentFinishCallBack callBack = UniversalPaymentCommonFragmentFinishCallBack.buildForBuyAlbum(mAlbumId);
                    BaseFragment2 fragment = null;
                    Fragment temp = null == mFragment ? null : mFragment.getParentFragment();
                    if (temp instanceof BaseFragment2) {
                        fragment = (BaseFragment2) temp;
                    }
                    return UniversalPayment.Util.enterRnWithCallBack(fragment, action.url, callBack);
                }

                @Override
                public boolean doOnTrackUnLock(CommonBehavior behavior, Track track, BehaviorAction action) {
                    if (null == track || null == track.getAlbum()) {
                        return false;
                    }
                    long currentTrackId = track.getDataId();
                    long currentAlbumId = track.getAlbum().getAlbumId();
                    PlayableModel playableModel = XmPlayerManager.getInstance(ToolUtil.getCtx()).getCurrSound();
                    //只需要刷新列表
                    UniversalPaymentUnLockCommonCallBack callBack = UniversalPaymentUnLockCommonCallBack.createTrackUnLockOnlyAlbumCallBack(track,currentTrackId, currentAlbumId);
                    boolean isExistFragment = XPlayPageRef.get() != null;//新版播放页面存在
                    if (isExistFragment && (playableModel != null && currentTrackId == playableModel.getDataId())) {
                        callBack = UniversalPaymentUnLockCommonCallBack.createTrackUnLockCallBack(track,currentTrackId, currentAlbumId);
                    }
                    return callBack.doTrackUnLock();
                }
            };
        }
        return actionOuterImp;
    }

    public void actionBuyTrack(Track track, boolean isFromAdChooseDialog) {
        if (track == null || !track.isPaid() || track.isAuthorized() || track.isFree()) {
            return;
        }
        if (!track.isHasCopyRight()) {
            CustomToast.showFailToast("版权方要求，该资源在该地区无法购买");
            return;
        }
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(context);
            // AdUnLockPaidManager.registerLoginStatus(track.getDataId());
            return;
        }
        if (getSingleAlbumPayManager() != null) {
            getSingleAlbumPayManager().buySinglePayAlbumTrack(track, false, isFromAdChooseDialog, BundleBuyDialogFragment1.FLAG_ALBUM_FRAGMENT_NEW);
        }
    }

    private SingleAlbumPayManager getSingleAlbumPayManager() {
        if (singleAlbumPayManager == null && payResultListener != null) {
            singleAlbumPayManager = new SingleAlbumPayManager(payResultListener);
            singleAlbumPayManager.setAlbumStatusChangedListener(albumStatusChangedListener);
            if (mAlbumMDataProvider != null && !TextUtils.isEmpty(mAlbumMDataProvider.getAlbumActivityParams())) {
                singleAlbumPayManager.setActivityTraceParams(mAlbumMDataProvider.getAlbumActivityParams());
            }
        }
        return singleAlbumPayManager;
    }

    public void onDestroy() {
        RouteServiceUtil.getDownloadService().unRegisterDownloadCallback(this);
    }

    @Override
    public int getConvertViewId() {
        return R.layout.main_item_track_view3;
    }

    @Override
    public BaseViewHolder buildHolder(View convertView) {
        return new ViewHolder(convertView);
    }

    @Override
    public Object getItem(int position) {
        Object obj = super.getItem(position);
        if (obj instanceof Track && mPlaysource > 0) {
            ((Track) obj).setPlaySource(mPlaysource);
        }
        return obj;
    }

    public void setCopyrightExtendInfo(@Nullable CopyrightExtendInfo copyrightExtendInfo) {
        mCopyrightExtendInfo = copyrightExtendInfo;
    }

    @Override
    public void onUnlockResult(boolean success, long trackId) {
        if (ToolUtil.isEmptyCollects(listData)) {
            return;
        }
        try {
            for (Track track : listData) {
                if (null != track && trackId == track.getDataId()) {
                    AdMakeVipLocalManager.getInstance().setAsAuthorizedTrack(track);
                }
            }
            notifyDataSetChanged();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onAdUnlockVipRewardSuccess(long albumId, long trackId) {
        if (albumId != mAlbumId || ToolUtil.isEmptyCollects(listData)) {
            return;
        }
        try {
            for (Track track : listData) {
                if (null != track && trackId == track.getDataId()) {
                    AdMakeVipLocalManager.getInstance().setAsAuthorizedTrack(track);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onAdUnlockVipRewardSuccess(Track track) {

    }

    private void setTrackDownloadStatus(Context context, ImageView view, int status) {
        if (view == null) {
            return;
        }
        switch (status) {
            case IDownloadStatus.DOWNLOAD_WAITING:
            case IDownloadStatus.DOWNLOADING:
                if (context == null) return;
                view.setVisibility(View.VISIBLE);
                view.setImageResource(R.drawable.main_album3_ic_list_loading);
                Animation operatingAnim = AnimationUtils.loadAnimation(context, com.ximalaya.ting.android.host.R.anim.host_loading_rotate);
                LinearInterpolator lin = new LinearInterpolator();
                operatingAnim.setInterpolator(lin);
                view.clearAnimation();
                view.startAnimation(operatingAnim);
                break;
            case IDownloadStatus.DOWNLOAD_FINISH:
                view.setVisibility(View.VISIBLE);
                view.clearAnimation();
                view.setImageResource(R.drawable.main_album_track_downloaded_icon);
                break;
            default:
                view.clearAnimation();
                view.setVisibility(View.GONE);
                break;
        }
    }

    @Override
    public void onDownloadProgress(@Nullable BaseDownloadTask downloadTask) {

    }

    @Override
    public void onCancel(@Nullable BaseDownloadTask downloadTask) {
        if (downloadTask == null || downloadTask.getTrack() == null) {
            return;
        }
        updateSingleItem(mListView, downloadTask.getTrack());
    }

    @Override
    public void onComplete(@Nullable BaseDownloadTask downloadTask) {
        if (downloadTask == null || downloadTask.getTrack() == null) {
            return;
        }
        updateSingleItem(mListView, downloadTask.getTrack());
    }

    @Override
    public void onUpdateTrack(@Nullable BaseDownloadTask downloadTask) {

    }

    @Override
    public void onStartNewTask(@Nullable BaseDownloadTask downloadTask) {
        if (downloadTask == null || downloadTask.getTrack() == null) {
            return;
        }
        updateSingleItem(mListView, downloadTask.getTrack());
    }

    @Override
    public void onError(@Nullable BaseDownloadTask downloadTask) {
        if (downloadTask == null || downloadTask.getTrack() == null) {
            return;
        }
        updateSingleItem(mListView, downloadTask.getTrack());
    }

    @Override
    public void onDelete() {
    }

    public static class ViewHolder extends BaseViewHolder {
        public View root;
        public TextView title;
        ImageView playingFlag;
        ImageView lastListenTag;
        ImageView downloadStatusIv;
        ImageView lockIv;
        View moreBtn;
        TextView tagTv;
        LinearLayout otherInfoLayout;
        RoundProgressBar progressBar;
        TextView orderNo;
        View clickMask;

        public ViewHolder(View convertView) {
            root = convertView;
            orderNo = convertView.findViewById(R.id.main_tv_order_no);
            title = convertView.findViewById(R.id.main_sound_name);
            playingFlag = convertView.findViewById(R.id.main_iv_playing_flag);
            lastListenTag = convertView.findViewById(R.id.main_iv_last_listen_tag);
            moreBtn = convertView.findViewById(R.id.main_iv_show_notes_more_btn);
            tagTv = convertView.findViewById(R.id.main_track_tags_tv);
            progressBar = convertView.findViewById(R.id.main_progress_bar);
            otherInfoLayout = convertView.findViewById(R.id.main_track_other_info_layout);
            downloadStatusIv = convertView.findViewById(R.id.main_iv_download_status);
            lockIv = convertView.findViewById(R.id.main_iv_lock);
            clickMask = convertView.findViewById(R.id.main_album3_track_click_mask);
        }
    }


    public interface OnCustomizeClickListener {
        void onCustomizeClick();
    }

    private OnCustomizeClickListener mOnCustomizeClickListener = null;

    public void setOnCustomClickListener(OnCustomizeClickListener onCustomizeClickListener) {
        mOnCustomizeClickListener = onCustomizeClickListener;
    }

    private static final SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
    private static final SimpleDateFormat sdf2 = new SimpleDateFormat("MM-dd", Locale.getDefault());
    public static String getFriendlyTimeStr(long l) {
        long curTime = System.currentTimeMillis();
        if (l <= 0 || curTime < l) {
            return countTimeNew(l);
        }
        long between = (curTime - l) / 1000;// 除以1000是为了转换成秒
        if (between < 60) {
            return "刚刚发布";
        }
        long month = between / (24 * 3600 * 30);
        long day = between / (24 * 3600);
        long hour = between / 3600;
        long minute = between / 60;
        if (month >= 12 || (!TimeHelper.isSameYear(l) && day >= 8)) {
            //超过12个月
            return sdf1.format(new Date(l));
        } else if (day >= 8) {
            //大于等于8天  x月x日
            return sdf2.format(new Date(l));
        } else if (day >= 1) {
            //小于1个月大于等于1天
            return day + "天前";
        } else if (hour >= 1) {
            //小于1天大于等于1个小时
            return hour + "小时前";
        } else if (minute >= 1) {
            //小于1个小时大于等于1分钟
            return minute + "分钟前";
        } else {
            return "刚刚发布";
        }
    }

    private static String countTimeNew(long t) {
        if (t <= 0) {
            return "";
        } else {
            return new SimpleDateFormat("yy/MM").format(new Date(t));
        }
    }

    private void adjustTvTextSize(TextView tv, int maxWidth, String text) {
        int avaiWidth = maxWidth - tv.getPaddingLeft() - tv.getPaddingRight();

        if (avaiWidth <= 0) {
            return;
        }

        TextPaint textPaintClone = new TextPaint(tv.getPaint());

        DisplayMetrics displayMetrics = tv.getContext().getResources().getDisplayMetrics();
        float trySize = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_SP, 13.0f, displayMetrics);
        // 先用最大字体写字
        textPaintClone.setTextSize(trySize);
        while (textPaintClone.measureText(text) > avaiWidth) {
            trySize--;
            textPaintClone.setTextSize(trySize);
        }

        tv.setTextSize(TypedValue.COMPLEX_UNIT_PX, trySize);
    }
}
