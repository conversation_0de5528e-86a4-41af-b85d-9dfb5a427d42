package com.ximalaya.ting.android.main.model.anchor;

import org.json.JSONObject;

/**
 * Created by zhifu.zhang on 3/2/2020 AD.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18362080562
 * @desc 当前登录用户与查询主播的关系
 */
public class AnchorRelationModel {

    private long uid;
    private boolean isFollow;
    private boolean isInBlackList;

    public void parse(JSONObject jsonObject) {
        if (jsonObject == null) {
            return;
        }
        if (jsonObject.has("uid")) {
            setUid(jsonObject.optLong("uid", 0));
        }
        if (jsonObject.has("isFollow")) {
            setFollow(jsonObject.optBoolean("isFollow"));
        }
        if (jsonObject.has("isInBlackList")) {
            setInBlackList(jsonObject.optBoolean("isInBlackList"));
        }
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public boolean isFollow() {
        return isFollow;
    }

    public void setFollow(boolean follow) {
        isFollow = follow;
    }

    public boolean isInBlackList() {
        return isInBlackList;
    }

    public void setInBlackList(boolean inBlackList) {
        isInBlackList = inBlackList;
    }
}
