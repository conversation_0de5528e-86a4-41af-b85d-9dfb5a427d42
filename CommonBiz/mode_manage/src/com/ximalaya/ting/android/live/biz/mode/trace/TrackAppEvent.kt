package com.ximalaya.ting.android.live.biz.mode.trace

import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.live.biz.mode.manager.LiveRoomExitManager
import com.ximalaya.ting.android.live.common.lib.manager.LiveRecordInfoManager
import com.ximalaya.ting.android.live.common.lib.utils.XmLiveRequestIdHelper
import com.ximalaya.ting.android.xmtrace.XMTraceApi

/**
 * Created by zhangying on 2021/9/12.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber ***********
 */

/**
 * 互娱统一退出弹框  弹框展示
 */
fun trackLiveExitDialogShow(dialogType: String) {
    XMTraceApi.Trace()
            .setMetaId(36400)
            .setServiceId("dialogView")
            .put("currPage", "LiveExitDetentionFragment")
            .put("dialogType", dialogType)
            .put("playsource", LiveRoomExitManager.sPlaySource.toString())
            .put(LiveRecordInfoManager.getInstance().baseProps)
            .put("roomMode", LiveRecordInfoManager.getInstance().entOrUgcRoomMode)
            .put("recordMode", LiveRecordInfoManager.getInstance().ugcRecordMode)
            .createTrace()
}


/**
 *互娱统一退出弹框-引导直播间  控件点击
 */
fun trackLiveExitRecommendDialogClick(
        position: String,
        recommendLiveId: String,
        recommendAnchorId: String,
        xmRequestId: String? = ""
) {
    XMTraceApi.Trace()
            .setMetaId(36403)
            .setServiceId("dialogClick")
            .put("recommendLiveId", recommendLiveId)
            .put("recommendAnchorId", recommendAnchorId)
            .put("currPage", "LiveExitRecommendFragment")
            .put("roomMode", LiveRecordInfoManager.getInstance().entOrUgcRoomMode)
            .put("recordMode", LiveRecordInfoManager.getInstance().ugcRecordMode)
            .put("dialogType", "2")
            .put("position", position)
            .put(LiveRecordInfoManager.getInstance().baseProps)
            .put(XmLiveRequestIdHelper.createTracePropsForClick(xmRequestId))
            .createTrace()
}


/**
 * 互娱统一退出弹框-按钮  弹框控件点击
 */
fun trackLiveExitDialogClick(
        dialogType: String,
        currPage: String,
        item: String
) {
    XMTraceApi.Trace()
            .setMetaId(36401)
            .setServiceId("dialogClick")
            .put("Item", item)
            .put("currPage", currPage)
            .put("roomMode", LiveRecordInfoManager.getInstance().entOrUgcRoomMode)
            .put("recordMode", LiveRecordInfoManager.getInstance().ugcRecordMode)
            .put("dialogType", dialogType)
            .put("playsource", LiveRoomExitManager.sPlaySource.toString())
            .put(LiveRecordInfoManager.getInstance().baseProps)
            .createTrace()
}

/**
 * 互娱统一退出弹框-引导直播间  弹框控件曝光
 */
fun trackLiveRecommendItemShow(
        position: String,
        recommendLiveId: String? = "",
        recommendAnchorId: String? = "",
        xmRequestId: String? = ""
) {
    XMTraceApi.Trace()
            .setMetaId(36402)
            .setServiceId("slipPage")
            .put("recommendLiveId", recommendLiveId)
            .put("recommendAnchorId", recommendAnchorId)
            .put("currPage", "RoomPathGuideFragment")
            .put("roomMode", LiveRecordInfoManager.getInstance().entOrUgcRoomMode)
            .put("recordMode", LiveRecordInfoManager.getInstance().ugcRecordMode)
            .put("dialogType", "2")
            .put("position", position)
            .put(LiveRecordInfoManager.getInstance().baseProps)
            .put(XmLiveRequestIdHelper.createTracePropsForSlipPage(xmRequestId, recommendLiveId))
            .createTrace()
}

/**
 * 自打赏引导弹框, 弹框展示
 */
fun liveSelfRewardDialogShow() {
    XMTraceApi.Trace()
            .setMetaId(39423)
            .setServiceId("dialogView")
            .put("currPage", "selfpay")
            .put("uid", UserInfoMannage.getUid().toString())
            .put("roomId", LiveRecordInfoManager.getInstance().roomId.toString())
            .createTrace()
}