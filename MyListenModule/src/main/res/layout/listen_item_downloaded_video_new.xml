<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:makeramen="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/host_bg_list_selector"
    android:descendantFocusability="blocksDescendants"
    android:paddingStart="@dimen/host_default_side_margin"
    android:paddingEnd="@dimen/host_y10"
    android:paddingBottom="@dimen/host_default_side_margin">

    <ImageView
        android:id="@+id/listen_batch_delete_icon"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_centerVertical="true"
        android:layout_marginEnd="12dp"
        android:contentDescription="@string/listen_content_description_delete"
        android:src="@drawable/host_uncheck_delete"
        android:visibility="gone" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/listen_csl_cover"
        android:layout_width="106dp"
        android:layout_height="60dp"
        android:layout_marginTop="12dp"
        android:layout_toEndOf="@id/listen_batch_delete_icon">


        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:id="@+id/listen_iv_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignWithParentIfMissing="true"
            android:contentDescription="声音封面图"
            android:scaleType="centerCrop"
            android:src="@drawable/host_default_album"
            makeramen:border_color="@color/listen_color_33000000"
            makeramen:border_width="1px"
            makeramen:corner_radius="4dp"
            makeramen:pressdown_shade="false"
            makeramen:round_background="false"
            tools:layout_editor_absoluteX="0dp"
            tools:layout_editor_absoluteY="0dp" />

        <ImageView
            android:id="@+id/listen_iv_play_panel"
            android:layout_width="26dp"
            android:layout_height="26dp"
            android:background="@drawable/host_bg_common_play_icon"
            android:contentDescription="播放，按钮"
            android:scaleType="centerCrop"
            makeramen:layout_constraintBottom_toBottomOf="parent"
            makeramen:layout_constraintEnd_toEndOf="parent"
            makeramen:layout_constraintStart_toStartOf="parent"
            makeramen:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/listen_play_icon"
            android:layout_width="16dp"
            android:layout_height="16dp"
            makeramen:layout_constraintBottom_toBottomOf="parent"
            makeramen:layout_constraintEnd_toEndOf="parent"
            makeramen:layout_constraintStart_toStartOf="parent"
            makeramen:layout_constraintTop_toTopOf="parent"
            android:contentDescription="@string/listen_content_description_play"
            android:tint="@color/host_color_white"
            android:src="@drawable/host_btn_play_btn_inside_fill_n_12"
            android:visibility="visible"
            tools:ignore="UseAppTint" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/listen_download_track_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignStart="@+id/listen_tv_subtitle"
        android:layout_alignTop="@+id/listen_csl_cover"
        android:layout_marginEnd="14dp"
        android:layout_toStartOf="@+id/listen_iv_del"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:textColor="@color/host_color_listTitleColor"
        style="@style/host_style_text_list_title"
        tools:text="专辑：经典专辑合集" />

    <TextView
        android:id="@+id/listen_tv_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/listen_download_track_title"
        android:layout_marginStart="12dp"
        android:layout_marginTop="@dimen/host_y4"
        android:layout_marginEnd="14dp"
        android:layout_toStartOf="@+id/listen_iv_del"
        android:layout_toEndOf="@+id/listen_csl_cover"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:text="标题内容最长为1行"
        android:textColor="@color/host_color_lightTextColor"
        style="@style/host_style_text_list_note_1"/>

    <LinearLayout
        android:id="@+id/listen_ll_track_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/listen_tv_subtitle"
        android:layout_alignStart="@id/listen_download_track_title"
        android:layout_marginTop="@dimen/host_y4"
        android:layout_toStartOf="@+id/listen_iv_del"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/listen_tv_total_time"
            style="@style/listen_album_info"
            android:drawableLeft="@drawable/host_ic_time_amount_n_line_regular_12"
            android:gravity="center_vertical"
            android:textColor="@color/host_color_lightTextColor"
            tools:text="00:00" />

        <TextView
            android:id="@+id/listen_tv_file_size"
            style="@style/listen_album_info"
            android:drawableLeft="@drawable/host_ic_file_amount_n_line_regular_12"
            android:gravity="center_vertical"
            android:textColor="@color/host_color_lightTextColor"
            tools:text="0" />

        <TextView
            android:id="@+id/listen_tv_play_schedule"
            style="@style/listen_album_info"
            android:layout_marginRight="0dp"
            android:maxLines="1"
            android:textColor="@color/host_color_yello1_alpha07"
            android:visibility="gone"
            tools:text="已播1%"
            tools:visibility="visible" />

    </LinearLayout>

    <ImageView
        android:id="@+id/listen_iv_del"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:contentDescription="@string/listen_content_description_delete"
        android:src="@drawable/host_ic_delete_n_n_line_regular_24"
        android:tint="@color/host_color_mediumTextColor"
        tools:ignore="UseAppTint" />

</RelativeLayout>