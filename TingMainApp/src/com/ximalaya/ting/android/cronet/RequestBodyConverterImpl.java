package com.ximalaya.ting.android.cronet;

import androidx.annotation.VisibleForTesting;

import com.google.common.base.Verify;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.Uninterruptibles;

import org.chromium.net.UploadDataProvider;
import org.chromium.net.UploadDataSink;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import okhttp3.RequestBody;
import okio.Buffer;
import okio.BufferedSink;
import okio.Okio;

final class RequestBodyConverterImpl implements RequestBodyConverter {
    private static final long IN_MEMORY_BODY_LENGTH_THRESHOLD_BYTES = 1048576L;
    private final InMemoryRequestBodyConverter inMemoryRequestBodyConverter;
    private final StreamingRequestBodyConverter streamingRequestBodyConverter;

    RequestBodyConverterImpl(InMemoryRequestBodyConverter inMemoryConverter, StreamingRequestBodyConverter streamingConverter) {
        this.inMemoryRequestBodyConverter = inMemoryConverter;
        this.streamingRequestBodyConverter = streamingConverter;
    }

    static RequestBodyConverterImpl create(ExecutorService bodyReaderExecutor) {
        return new RequestBodyConverterImpl(new InMemoryRequestBodyConverter(), new StreamingRequestBodyConverter(bodyReaderExecutor));
    }

    public UploadDataProvider convertRequestBody(RequestBody requestBody, int writeTimeoutMillis) throws IOException {
        long contentLength = requestBody.contentLength();
        return contentLength != -1L && contentLength <= 1048576L ? this.inMemoryRequestBodyConverter.convertRequestBody(requestBody, writeTimeoutMillis) : this.streamingRequestBodyConverter.convertRequestBody(requestBody, writeTimeoutMillis);
    }

    @VisibleForTesting
    static final class InMemoryRequestBodyConverter implements RequestBodyConverter {
        InMemoryRequestBodyConverter() {
        }

        public UploadDataProvider convertRequestBody(final RequestBody requestBody, int writeTimeoutMillis) throws IOException {
            final long length = requestBody.contentLength();
            if (length >= 0L && length <= 1048576L) {
                return new UploadDataProvider() {
                    private volatile boolean isMaterialized = false;
                    private final Buffer materializedBody = new Buffer();

                    public long getLength() {
                        return length;
                    }

                    public void read(UploadDataSink uploadDataSink, ByteBuffer byteBuffer) throws IOException {
                        if (!this.isMaterialized) {
                            requestBody.writeTo(this.materializedBody);
                            this.materializedBody.flush();
                            this.isMaterialized = true;
                            long reportedLength = this.getLength();
                            long actualLength = this.materializedBody.size();
                            if (actualLength != reportedLength) {
                                throw new IOException("Expected " + reportedLength + " bytes but got " + actualLength);
                            }
                        }

                        if (this.materializedBody.read(byteBuffer) == -1) {
                            throw new IllegalStateException("The source has been exhausted but we expected more!");
                        } else {
                            uploadDataSink.onReadSucceeded(false);
                        }
                    }

                    public void rewind(UploadDataSink uploadDataSink) {
                        uploadDataSink.onRewindError(new UnsupportedOperationException());
                    }
                };
            } else {
                throw new IOException("Expected definite length less than 1048576but got " + length);
            }
        }
    }

    @VisibleForTesting
    static final class StreamingRequestBodyConverter implements RequestBodyConverter {
        private final ExecutorService readerExecutor;

        StreamingRequestBodyConverter(ExecutorService readerExecutor) {
            this.readerExecutor = readerExecutor;
        }

        public UploadDataProvider convertRequestBody(RequestBody requestBody, int writeTimeoutMillis) {
            return new StreamingUploadDataProvider(requestBody, new UploadBodyDataBroker(), this.readerExecutor, (long)writeTimeoutMillis);
        }

        private static class StreamingUploadDataProvider extends UploadDataProvider {
            private final RequestBody okHttpRequestBody;
            private final UploadBodyDataBroker broker;
            private final ListeningExecutorService readTaskExecutor;
            private final long writeTimeoutMillis;
            private ListenableFuture<?> readTaskFuture;
            private long totalBytesReadFromOkHttp;

            private StreamingUploadDataProvider(RequestBody okHttpRequestBody, UploadBodyDataBroker broker, ExecutorService readTaskExecutor, long writeTimeoutMillis) {
                this.okHttpRequestBody = okHttpRequestBody;
                this.broker = broker;
                if (readTaskExecutor instanceof ListeningExecutorService) {
                    this.readTaskExecutor = (ListeningExecutorService)readTaskExecutor;
                } else {
                    this.readTaskExecutor = MoreExecutors.listeningDecorator(readTaskExecutor);
                }

                this.writeTimeoutMillis = writeTimeoutMillis == 0L ? 2147483647L : writeTimeoutMillis;
            }

            public long getLength() throws IOException {
                return this.okHttpRequestBody.contentLength();
            }

            public void read(UploadDataSink uploadDataSink, ByteBuffer byteBuffer) throws IOException {
                this.ensureReadTaskStarted();
                if (this.getLength() == -1L) {
                    this.readUnknownBodyLength(uploadDataSink, byteBuffer);
                } else {
                    this.readKnownBodyLength(uploadDataSink, byteBuffer);
                }

            }

            private void readKnownBodyLength(UploadDataSink uploadDataSink, ByteBuffer byteBuffer) throws IOException {
                try {
                    UploadBodyDataBroker.ReadResult readResult = this.readFromOkHttp(byteBuffer);
                    if (this.totalBytesReadFromOkHttp > this.getLength()) {
                        throw prepareBodyTooLongException(this.getLength(), this.totalBytesReadFromOkHttp);
                    }

                    if (this.totalBytesReadFromOkHttp < this.getLength()) {
                        switch (readResult) {
                            case SUCCESS:
                                uploadDataSink.onReadSucceeded(false);
                            default:
                                return;
                            case END_OF_BODY:
                                throw new IOException("The source has been exhausted but we expected more data!");
                        }
                    }

                    this.handleLastBodyRead(uploadDataSink, byteBuffer);
                } catch (ExecutionException | TimeoutException var4) {
                    Exception e = var4;
                    this.readTaskFuture.cancel(true);
                    uploadDataSink.onReadError(new IOException(e));
                }

            }

            private void handleLastBodyRead(UploadDataSink uploadDataSink, ByteBuffer filledByteBuffer) throws IOException, TimeoutException, ExecutionException {
                int bufferPosition = filledByteBuffer.position();
                filledByteBuffer.position(0);
                UploadBodyDataBroker.ReadResult readResult = this.readFromOkHttp(filledByteBuffer);
                if (!readResult.equals(UploadBodyDataBroker.ReadResult.END_OF_BODY)) {
                    throw prepareBodyTooLongException(this.getLength(), this.totalBytesReadFromOkHttp);
                } else {
                    Verify.verify(filledByteBuffer.position() == 0, "END_OF_BODY reads shouldn't write anything to the buffer", new Object[0]);
                    filledByteBuffer.position(bufferPosition);
                    uploadDataSink.onReadSucceeded(false);
                }
            }

            private void readUnknownBodyLength(UploadDataSink uploadDataSink, ByteBuffer byteBuffer) {
                try {
                    UploadBodyDataBroker.ReadResult readResult = this.readFromOkHttp(byteBuffer);
                    uploadDataSink.onReadSucceeded(readResult.equals(UploadBodyDataBroker.ReadResult.END_OF_BODY));
                } catch (ExecutionException | TimeoutException var4) {
                    Exception e = var4;
                    this.readTaskFuture.cancel(true);
                    uploadDataSink.onReadError(new IOException(e));
                }

            }

            private void ensureReadTaskStarted() {
                if (this.readTaskFuture == null) {
                    this.readTaskFuture = this.readTaskExecutor.submit(() -> {
                        BufferedSink bufferedSink = Okio.buffer(this.broker);
                        this.okHttpRequestBody.writeTo(bufferedSink);
                        bufferedSink.flush();
                        this.broker.handleEndOfStreamSignal();
                        return null;
                    });
                    Futures.addCallback(this.readTaskFuture, new FutureCallback<Object>() {
                        public void onSuccess(Object result) {
                        }

                        public void onFailure(Throwable t) {
                            StreamingUploadDataProvider.this.broker.setBackgroundReadError(t);
                        }
                    }, MoreExecutors.directExecutor());
                }

            }

            private UploadBodyDataBroker.ReadResult readFromOkHttp(ByteBuffer byteBuffer) throws TimeoutException, ExecutionException {
                int positionBeforeRead = byteBuffer.position();
                UploadBodyDataBroker.ReadResult readResult = (UploadBodyDataBroker.ReadResult)Uninterruptibles.getUninterruptibly(this.broker.enqueueBodyRead(byteBuffer), this.writeTimeoutMillis, TimeUnit.MILLISECONDS);
                int bytesRead = byteBuffer.position() - positionBeforeRead;
                this.totalBytesReadFromOkHttp += (long)bytesRead;
                return readResult;
            }

            private static IOException prepareBodyTooLongException(long expectedLength, long minActualLength) {
                return new IOException("Expected " + expectedLength + " bytes but got at least " + minActualLength);
            }

            public void rewind(UploadDataSink uploadDataSink) {
                uploadDataSink.onRewindError(new UnsupportedOperationException("Rewind is not supported!"));
            }
        }
    }
}
