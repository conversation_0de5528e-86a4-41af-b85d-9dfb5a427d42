package com.ximalaya.ting.android.host.hybrid.utils;

import android.os.Environment;
import android.os.StatFs;
import android.text.TextUtils;

import com.ximalaya.ting.android.hybridview.component.utils.StreamUtils;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * Created by vive on 2017/4/13.
 * <AUTHOR>
 */

public class FileUtils {

    public static boolean copy(InputStream is, File copyTo) throws IOException {
        if (is == null || copyTo == null || copyTo.isDirectory()) {
            throw new IllegalArgumentException();
        }
        DataInputStream in = null;
        DataOutputStream out = null;
        try {

            if (copyTo.exists()) {
                copyTo.delete();
            }
            in = new DataInputStream(new BufferedInputStream(is));

            byte[] date = new byte[in.available()];

            in.read(date);

            out = new DataOutputStream(new BufferedOutputStream(new FileOutputStream(copyTo)));
            out.write(date);
            return true;
        } catch (Exception e) {
            return false;
        } finally {

            StreamUtils.closeQuietly(in);
            StreamUtils.closeQuietly(out);
        }
    }
    public static boolean copyFiles(File copyFrom, File copyTo) throws IOException {
        if (copyFrom == null || copyTo == null || !copyFrom.exists()) {
            throw new IllegalArgumentException();
        }
        boolean res = true;
        boolean copyToAlreadExists = true;
        try {
            if (copyFrom.isDirectory()) {
                if (copyTo.exists() && !copyTo.isDirectory()) {
                    deleteFile(copyTo);
                }
                if (!copyTo.exists()) {
                    copyTo.mkdirs();
                    copyToAlreadExists = false;
                }
                File[] files = copyFrom.listFiles();
                for (File copyFromChildFile : files) {
                    res = copyFiles(copyFromChildFile, new File(copyTo, copyFromChildFile.getName()));
                    if (!res) {
                        return res;
                    }
                }
            } else {
                deleteFile(copyTo);
                copyToAlreadExists = false;
                copyTo.getParentFile().mkdirs();
                copyTo.createNewFile();
                res = copy(copyFrom, copyTo);
            }
        } finally {
            if (!copyToAlreadExists && !res && copyTo.exists()) {
                deleteFile(copyTo);
            }
        }
        return res;
    }

    public static boolean copy(File copyFrom, File copyTo) throws IOException {
        if (copyFrom == null || copyTo == null || copyFrom.isDirectory() || copyTo.isDirectory()) {
            throw new IllegalArgumentException();
        }
        DataInputStream in = null;
        DataOutputStream out = null;
        try {
            in = new DataInputStream(new BufferedInputStream(new FileInputStream(copyFrom)));

            byte[] date = new byte[in.available()];

            in.read(date);

            out = new DataOutputStream(new BufferedOutputStream(new FileOutputStream(copyTo)));
            out.write(date);
            out.flush();
            return true;
        } finally {

            StreamUtils.closeQuietly(in);
            StreamUtils.closeQuietly(out);
        }
    }

    public static void deleteFile(File file) {
        if (file == null || !file.exists()) {
            return;
        }

        if (file.isFile()) {
            file.delete();
            return;
        }
        String[] fileNames = file.list();
        if (fileNames == null || fileNames.length == 0) {
            file.delete();
        } else {
            File[] files = file.listFiles();
            if (files == null) {
                file.delete();
                return;
            }
            for (int i = 0; i < files.length; i++) {
                deleteFile(files[i]);
                files[i].delete();
            }
            if (file.exists()) {
                file.delete();
            }
        }
    }

    /**
     * Reads the file into a byte array.
     *
     * @param file The file
     * @return a byte array or {@code null} if file is null, file not exist, file is directory or error occurred.
     */
    public static byte[] readAsBytes(File file) {
        if (file == null || !file.exists() || file.isDirectory()) {
            return null;
        }
        InputStream is = null;
        try {
            is = new FileInputStream(file);
            return StreamUtils.copyStreamToByteArray(is);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        } finally {
            StreamUtils.closeQuietly(is);
        }
    }

    /**
     * Reads the file into string, with "UTF-8" charset.
     *
     * @param file The file
     * @return a byte array or {@code null} if file is null, file not exist, file is directory or error occurred.
     */
    public static String readAsString(File file) {
        if (file == null || !file.exists() || file.isDirectory()) {
            return null;
        }
        InputStream is = null;
        try {
            is = new FileInputStream(file);
            return StreamUtils.copyStreamToString(is);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        } finally {
            StreamUtils.closeQuietly(is);
        }
    }

    /**
     * SDCARD是否存
     */
    public static boolean externalMemoryAvailable() {
        return android.os.Environment.getExternalStorageState().equals(
                android.os.Environment.MEDIA_MOUNTED);
    }

    /**
     * 获取手机内部剩余存储空间
     *
     * @return
     */
    public static long getAvailableInternalMemorySize() {
        File path = Environment.getDataDirectory();
        StatFs stat = new StatFs(path.getPath());
        long blockSize = stat.getBlockSize();
        long availableBlocks = stat.getAvailableBlocks();
        return availableBlocks * blockSize;
    }

    /**
     * 获取手机内部总的存储空间
     *
     * @return
     */
    public static long getTotalInternalMemorySize() {
        File path = Environment.getDataDirectory();
        StatFs stat = new StatFs(path.getPath());
        long blockSize = stat.getBlockSize();
        long totalBlocks = stat.getBlockCount();
        return totalBlocks * blockSize;
    }

    /**
     * 获取SDCARD剩余存储空间
     *
     * @return
     */
    public static long getAvailableExternalMemorySize() {
        if (externalMemoryAvailable()) {
            File path = Environment.getExternalStorageDirectory();
            StatFs stat = new StatFs(path.getPath());
            long blockSize = stat.getBlockSize();
            long availableBlocks = stat.getAvailableBlocks();
            return availableBlocks * blockSize;
        } else {
            return 0;
        }
    }

    /**
     * 获取SDCARD总的存储空间
     *
     * @return
     */
    public static long getTotalExternalMemorySize() {
        if (externalMemoryAvailable()) {
            File path = Environment.getExternalStorageDirectory();
            StatFs stat = new StatFs(path.getPath());
            long blockSize = stat.getBlockSize();
            long totalBlocks = stat.getBlockCount();
            return totalBlocks * blockSize;
        } else {
            return 0;
        }
    }

    /**
     * check if the file is exist.
     *
     * @param path
     * @return
     */
    public static boolean fileIsExist(String path) {
        if (TextUtils.isEmpty(path)) {
            return false;
        }
        File temp = new File(path);
        if (temp != null && temp.isFile() && temp.exists()) {
            return true;
        }
        return false;
    }
}
