package com.ximalaya.ting.android.host.hybrid.providerSdk.account;


import android.net.Uri;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.host.fragment.web.InternalDomainCheck;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.login.LoginHelper;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.hybridview.IHybridContainer;
import com.ximalaya.ting.android.hybridview.NativeResponse;
import com.ximalaya.ting.android.hybridview.constant.Constant;
import com.ximalaya.ting.android.hybridview.provider.BaseAction;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;

import org.json.JSONException;
import org.json.JSONObject;

import static com.ximalaya.ting.android.host.manager.account.UserInfoMannage.hasLogined;

/**
 * Created by chengyun.wu on 2017/5/10.
 * <AUTHOR>
 */

public class BaseJsSdkAccountAction extends BaseAction {

    protected void getAccountCallBackParams(IHybridContainer hybridContainer, final AsyncCallback callback, final boolean needLogin){
        try {
            final String url = hybridContainer.getWebViewLastLoadUrl();
            Uri uri = Uri.parse(url);
            final String domain = uri.getHost();
            final LoginInfoModelNew loginInfoModel = UserInfoMannage.getInstance().getUser();
            if (!hasLogined() || loginInfoModel == null) {
                JSONObject object = new JSONObject();
                object.put("isLogin", Boolean.FALSE);
                callback.callback(NativeResponse.success(object));
            } else {
                boolean isInternalDomain = domain != null && (InternalDomainCheck.getInstance().isInternalDomain(domain)
                        || Constant.HOST_COMPONENT.equals(domain));//xima内部域名
                if(isInternalDomain) {
                    try {
                        JSONObject jsonObject = new JSONObject();

                        jsonObject.put("uid", loginInfoModel.getUid());
                        jsonObject.put("imgUrl", loginInfoModel.getMobileSmallLogo());
                        jsonObject.put("token", loginInfoModel.getToken());
                        jsonObject.put("nickName", loginInfoModel.getNickname());
                        if(needLogin){
                            jsonObject.put("isLogin", Boolean.TRUE);
                            jsonObject.put("isNew", loginInfoModel.isFirst());
                        }
                        callback.callback(NativeResponse.success(jsonObject));
                    }catch (JSONException e){
                        e.printStackTrace();
                    }
                }else {
                    LoginHelper.getFakeToken(UserInfoMannage.getInstance().getUser(), UserInfoMannage.getUid(), new IDataCallBack<String>() {
                        @Override
                        public void onSuccess(@Nullable String object) {
                            try {
                                JSONObject jsonObject = new JSONObject();
                                jsonObject.put("uid", loginInfoModel.getUid());
                                jsonObject.put("imgUrl", loginInfoModel.getMobileSmallLogo());
                                if(needLogin){
                                    jsonObject.put("isLogin", Boolean.TRUE);
                                    jsonObject.put("isNew", loginInfoModel.isFirst());
                                }
                                if(!TextUtils.isEmpty(object)) {
                                    jsonObject.put("token", object);
                                }
                                jsonObject.put("nickName", loginInfoModel.getNickname());
                                callback.callback(NativeResponse.success(jsonObject));
                            }catch (JSONException e){
                                e.printStackTrace();
                            }
                        }

                        @Override
                        public void onError(int code, String message) {
                            callback.callback(NativeResponse.fail(-1, message));
                        }
                    } ,true);
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
            callback.callback(NativeResponse.fail(-1, "JSONException"));
        }
    }


    @Override
    public boolean needStatRunloop() {
        return false;
    }
}
