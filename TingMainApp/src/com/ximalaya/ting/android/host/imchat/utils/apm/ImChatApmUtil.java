package com.ximalaya.ting.android.host.imchat.utils.apm;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.ximalaya.pubsubsdk.model.PubSubApmInfo;
import com.ximalaya.ting.android.im.base.constants.XmIMConstans;
import com.ximalaya.ting.android.im.base.model.apm.ImNetApmInfo;
import com.ximalaya.ting.android.liveim.lib.connpair.ConnPairApmInfo;

/**
 * apm数据转化工具类
 *
 * <AUTHOR>
 * @desc 文件描述
 * @email <EMAIL>
 * @wiki 说明文档的链接地址
 * @server 服务端开发人员放在这里
 * @since 2020-06-29 15:16
 */
public class ImChatApmUtil {


    /**
     * 判断是否符合上报数据格式
     *
     * @param apmInfo 上报数据
     * @return 判断值
     */
    public static boolean isAbleToUploadApm(@NonNull ImNetApmInfo apmInfo) {

        if (apmInfo.currentPort <= 0 || TextUtils.isEmpty(apmInfo.currentHost)) {
            return false;
        }

        if (apmInfo.isSuccess) {//成功的操作

            if (TextUtils.equals(apmInfo.processTag, ImNetApmInfo.TAG_CONN)) {

                if (apmInfo.connectTime < 0
                        || apmInfo.connectTime > (XmIMConstans.TIMEOUT_SOCKET_CONNECT_THRESHOLD_MS + 500)) {
                    return false;
                }

            } else if (TextUtils.equals(apmInfo.processTag, ImNetApmInfo.TAG_JOIN)) {

                if (apmInfo.sendProcessTime < 0
                        || apmInfo.sendProcessTime > (XmIMConstans.TIMEOUT_SOCKET_CONNECT_THRESHOLD_MS + 500)) {
                    return false;
                }

            } else if (TextUtils.equals(apmInfo.processTag, ImNetApmInfo.TAG_SEND)) {

                if (TextUtils.isEmpty(apmInfo.sendMsgTypeName)) {
                    return false;
                }

                if (apmInfo.sendProcessTime < 0
                        || apmInfo.sendProcessTime > (XmIMConstans.TIMEOUT_THRESHOLD_MS + 500)) {
                    return false;
                }
            } else {
                return false;
            }

        } else {

            if (apmInfo.errCode <= 0) {
                return false;
            }

        }

        return true;
    }


    /**
     * 判断是否符合上报数据格式
     *
     * @param apmInfo 上报数据
     * @return 判断值
     */
    public static boolean isAbleToUploadApm(@NonNull ConnPairApmInfo apmInfo) {
        if (apmInfo.currentPort <= 0 || TextUtils.isEmpty(apmInfo.currentHost)) {
            return false;
        }
        //成功的操作
        if (apmInfo.isSuccess) {

            if (TextUtils.equals(apmInfo.processTag, ImNetApmInfo.TAG_CONN)) {

                if (apmInfo.connectTime < 0
                        || apmInfo.connectTime > (XmIMConstans.TIMEOUT_SOCKET_CONNECT_THRESHOLD_MS + 500)) {
                    return false;
                }

            } else if (TextUtils.equals(apmInfo.processTag, ImNetApmInfo.TAG_JOIN)) {

                if (apmInfo.sendProcessTime < 0
                        || apmInfo.sendProcessTime > (XmIMConstans.TIMEOUT_SOCKET_CONNECT_THRESHOLD_MS + 500)) {
                    return false;
                }

            } else if (TextUtils.equals(apmInfo.processTag, ImNetApmInfo.TAG_SEND)) {

                if (TextUtils.isEmpty(apmInfo.sendMsgTypeName)) {
                    return false;
                }

                if (apmInfo.sendProcessTime < 0
                        || apmInfo.sendProcessTime > (XmIMConstans.TIMEOUT_THRESHOLD_MS + 500)) {
                    return false;
                }
            } else {
                return false;
            }

        } else {

            if (apmInfo.errCode <= 0) {
                return false;
            }

        }

        return true;
    }

    /**
     * 判断是否符合上报数据格式
     *
     * @param apmInfo 上报数据
     * @return 判断值
     */
    public static boolean isAbleToUploadApm(@NonNull PubSubApmInfo apmInfo) {
        if (apmInfo.getCurrentPort() <= 0 || TextUtils.isEmpty(apmInfo.getCurrentHost())) {
            return false;
        }

        if (apmInfo.isSuccess()) {//成功的操作

            if (TextUtils.equals(apmInfo.getProcessTag(), ImNetApmInfo.TAG_CONN)) {

                if (apmInfo.getConnectTime() < 0
                        || apmInfo.getConnectTime() > (XmIMConstans.TIMEOUT_SOCKET_CONNECT_THRESHOLD_MS + 500)) {
                    return false;
                }

            } else if (TextUtils.equals(apmInfo.getProcessTag(), ImNetApmInfo.TAG_JOIN)) {

                if (apmInfo.getSendProcessTime() < 0
                        || apmInfo.getSendProcessTime() > (XmIMConstans.TIMEOUT_SOCKET_CONNECT_THRESHOLD_MS + 500)) {
                    return false;
                }

            } else if (TextUtils.equals(apmInfo.getProcessTag(), ImNetApmInfo.TAG_SEND)) {

                if (TextUtils.isEmpty(apmInfo.getSendMsgTypeName())) {
                    return false;
                }

                if (apmInfo.getSendProcessTime() < 0
                        || apmInfo.getSendProcessTime() > (XmIMConstans.TIMEOUT_THRESHOLD_MS + 500)) {
                    return false;
                }
            } else {
                return false;
            }

        } else {

            if (apmInfo.getErrCode() <= 0) {
                return false;
            }

        }

        return true;
    }


    public static String getImTCPUrl(@NonNull ImNetApmInfo apmInfo) {

        if (TextUtils.isEmpty(apmInfo.currentHost)) {
            return null;
        }

        return apmInfo.currentHost.replace(".", "-") + ":" + apmInfo.currentPort;

    }


}
