package com.ximalaya.ting.android.host.model.radio;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ximalaya.ting.android.host.data.model.live.RadioM;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.common.JsonUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by zhifu.zhang on 6/9/2020 AD.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18362080562
 * @desc
 */
public class RadioModuleModel {

    public static final String FOCUS = "FOCUS";//头部调频
    public static final String RECOMMEND = "RECOMMEND";//推荐
    public static final String SEARCH = "SEARCH";//搜所
    public static final String LOCAL = "LOCAL";//本地电台

    private int id;
    private String name;
    private int sort;
    private String type;
    private List<RadioM> radios;
    private List<RadioLocationModel> locations;
    private List<RadioCategoryModel> categories;
    private List<AlbumM> albums;
    private RadioLocationModel mCurrentLocation;//当前选中的地区，默认取数组中第一条，搜索使用
    private RadioCategoryModel mCurrentCategory;//当前选中的分类，默认取数组中第一条，搜索使用

    public RadioModuleModel() {

    }

    public RadioModuleModel(JSONObject jsonObject) {
        if (jsonObject == null) {
            return;
        }
        try {
            setId((Integer) JsonUtil.optJsonForClass(Integer.class, jsonObject, "id"));
            setName((String) JsonUtil.optJsonForClass(String.class, jsonObject, "name"));
            setSort((Integer) JsonUtil.optJsonForClass(Integer.class, jsonObject, "sort"));
            setType((String) JsonUtil.optJsonForClass(String.class, jsonObject, "type"));
            Gson gson = new Gson();
            if (jsonObject.has("radios")) {
                JSONArray jsonArray = jsonObject.optJSONArray("radios");
                if (jsonArray != null && jsonArray.length() > 0) {
                    radios = new ArrayList<>();
                    for (int i = 0; i < jsonArray.length(); i++) {
                        radios.add(new RadioM(jsonArray.optString(i)));
                    }
                }
            }
            if (jsonObject.has("locations")) {
                locations = gson.fromJson(jsonObject.optString("locations"), new TypeToken<List<RadioLocationModel>>() {}.getType());
            }
            if (jsonObject.has("categories")) {
                categories = gson.fromJson(jsonObject.optString("categories"), new TypeToken<List<RadioCategoryModel>>() {}.getType());
            }
            if (jsonObject.has("albums")) {
                JSONArray jsonArray = jsonObject.optJSONArray("albums");
                if (jsonArray != null && jsonArray.length() > 0) {
                    albums = new ArrayList<>();
                    for (int i = 0; i < jsonArray.length(); i++) {
                        albums.add(new AlbumM(jsonArray.optString(i)));
                    }
                }
            }
            if (!ToolUtil.isEmptyCollects(locations)) {
                setCurrentLocation(locations.get(0));
            }
            if (!ToolUtil.isEmptyCollects(categories)) {
                setCurrentCategory(categories.get(0));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }

    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<RadioM> getRadios() {
        return radios;
    }

    public void setRadios(List<RadioM> radios) {
        this.radios = radios;
    }

    public List<RadioLocationModel> getLocations() {
        return locations;
    }

    public void setLocations(List<RadioLocationModel> locations) {
        this.locations = locations;
    }

    public List<RadioCategoryModel> getCategories() {
        return categories;
    }

    public void setCategories(List<RadioCategoryModel> categories) {
        this.categories = categories;
    }

    public RadioLocationModel getCurrentLocation() {
        return mCurrentLocation;
    }

    public void setCurrentLocation(RadioLocationModel mCurrentLocation) {
        this.mCurrentLocation = mCurrentLocation;
    }

    public RadioCategoryModel getCurrentCategory() {
        return mCurrentCategory;
    }

    public void setCurrentCategory(RadioCategoryModel mCurrentCategory) {
        this.mCurrentCategory = mCurrentCategory;
    }

    public List<AlbumM> getAlbums() {
        return albums;
    }

    public void setAlbums(List<AlbumM> albums) {
        this.albums = albums;
    }
}
