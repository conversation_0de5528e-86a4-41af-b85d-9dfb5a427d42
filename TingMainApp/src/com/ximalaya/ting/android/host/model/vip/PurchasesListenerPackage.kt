package com.ximalaya.ting.android.host.model.vip

import com.google.gson.annotations.SerializedName

/**
 * Created on 2024/9/24.
 * <AUTHOR>
 * @email <EMAIL>
 */
data class PurchasesListenerPackage(
    val maxPageId: Int,
    val totalCount: Int,
    val pageSize: Int,
    val pageId: Int,
    @SerializedName("data")
    val listenerPackageList: List<ListenerPackageModel?>,
)

data class ListenerPackageModel(
    @SerializedName("statusDesc") val statusDesc: String? = null, // 状态描述文案 2024-10-30 到期
    @SerializedName("contentDesc") val contentDesc: String? = null, // 内容描述文案 20张专辑
    @SerializedName("title") val title: String? = null,
    @SerializedName("coverPath") val coverPath: String? = null,
    @SerializedName("url") val url: String? = null, // 跳转链接
    @SerializedName("contentId") val contentId: Long,
    var xmRequestId: String = ""
)