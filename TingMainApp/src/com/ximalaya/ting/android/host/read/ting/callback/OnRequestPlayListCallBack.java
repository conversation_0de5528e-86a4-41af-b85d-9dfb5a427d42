package com.ximalaya.ting.android.host.read.ting.callback;

import com.ximalaya.ting.android.opensdk.model.track.Track;

import java.util.List;

public interface OnRequestPlayListCallBack {
    void onRequestPlayListSuccess(List<Track> list, boolean hasMorePage, boolean isNextPage);

    void onRequestPlayListError(int code, String message, boolean isNextPage);

    default void onPlayServicePlayListChange() {

    }
}
