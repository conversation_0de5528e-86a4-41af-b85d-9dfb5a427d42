package com.ximalaya.ting.android.host.manager.ad;

import com.ximalaya.ting.android.ad.parallelload.ParallelLoadSDKState;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;

import java.util.Map;

import androidx.annotation.Nullable;

/**
 * Created by le.xin on 2020/5/29.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class ThirdAdLoadParams {
    private String positionName;
    private long requestTime;
    private int categoryId;

    private boolean hasVideo;
    private int minVideoDuration;   // 单位s
    private int maxVideoDuration;   // 单位s
    private boolean videoAutoPlay;

    private int dspRequestTimeMs;   // dsp 请求超时时间

    private boolean virtualAdRemovedOnDefer = true; // 虚拟广告位顺延的时候删除掉

    @Nullable
    private Map<Advertis, ParallelLoadSDKState> parallelLoadSDKStateMap;    // 并行预加载的请求状态

    private ThirdAdLoadParams() {

    }

    public ThirdAdLoadParams(String positionName, long requestTime) {
        this.positionName = positionName;
        this.requestTime = requestTime;
    }

    public void setVideoParams(boolean videoAutoPlay, int minVideoDuration, int maxVideoDuration) {
        hasVideo = true;
        this.videoAutoPlay = videoAutoPlay;
        this.minVideoDuration = minVideoDuration;
        this.maxVideoDuration = maxVideoDuration;
    }

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public long getRequestTime() {
        return requestTime;
    }

    public void setRequestTime(long requestTime) {
        this.requestTime = requestTime;
    }

    public int getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
    }

    public boolean isHasVideo() {
        return hasVideo;
    }

    public int getMinVideoDuration() {
        return minVideoDuration;
    }

    public int getMaxVideoDuration() {
        return maxVideoDuration;
    }

    public boolean isVideoAutoPlay() {
        return videoAutoPlay;
    }

    public int getDspRequestTimeMs() {
        return dspRequestTimeMs;
    }

    public void setDspRequestTimeMs(int dspRequestTimeMs) {
        this.dspRequestTimeMs = dspRequestTimeMs;
    }

    @Nullable
    public Map<Advertis, ParallelLoadSDKState> getParallelLoadSDKStateMap() {
        return parallelLoadSDKStateMap;
    }

    public void setParallelLoadSDKStateMap(@Nullable Map<Advertis, ParallelLoadSDKState> parallelLoadSDKStateMap) {
        this.parallelLoadSDKStateMap = parallelLoadSDKStateMap;
    }

    public boolean isVirtualAdRemovedOnDefer() {
        return virtualAdRemovedOnDefer;
    }

    public void setVirtualAdRemovedOnDefer(boolean virtualAdRemovedOnDefer) {
        this.virtualAdRemovedOnDefer = virtualAdRemovedOnDefer;
    }
}
