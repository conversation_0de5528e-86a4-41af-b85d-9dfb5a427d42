package com.ximalaya.ting.android.host.manager.notification;

import androidx.annotation.Nullable;

/**
 * 内push 行为（滑走，点击等）/状态（展示，消失，不让展示等）行为监听
 */
public interface IPushStateListener {
    String PUSH_REJECT_REASON_EXPIRED = "频控";

    int PUSH_MSG_CODE_QUEUED = 1; // 已接收到
    int PUSH_MSG_CODE_DISPLAYED = 2; // 展示
    int PUSH_MSG_CODE_DISMISSED = 3; // 消失
    int PUSH_MSG_CODE_DISCARDED = 4; // 抛弃（业务自己在弹出的时候检查了下当前是否可以弹出）
    int PUSH_MSG_CODE_EXPIRED = 5; // 超时抛弃
    int PUSH_MSG_CODE_CLICKED = 6; // 点击

    /**
     * 消息状态回调
     */
    void onPushAction(int code, PushTask task, @Nullable String extra);

    /**
     * app 亮/熄屏 状态回调
     */
    void onAppResumeState(boolean isForeground);
}
