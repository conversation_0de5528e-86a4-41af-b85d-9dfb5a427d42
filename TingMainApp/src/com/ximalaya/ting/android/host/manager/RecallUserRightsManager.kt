package com.ximalaya.ting.android.host.manager

import com.google.gson.annotations.SerializedName
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost
import com.ximalaya.ting.android.host.fragment.dialog.RecallUserRightsDialog
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.model.album.AlbumM
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import org.json.JSONObject

object RecallUserRightsManager {

    fun checkCanShow(): Boolean {
        val lastShowTime = MMKVUtil.getInstance().getLong(PreferenceConstantsInHost
                .KEY_LAST_REQUEST_NEED_SHOW_RECALL_USER_RIGHTS_DIALOG_TIME, 0)
        if (lastShowTime == 0L || System.currentTimeMillis() - lastShowTime >= 28 * 24 * 3600 * 1000L) {
            return true
        }
        return false
    }

    fun requestRecallUserRightsData(callback: IDataCallBack<RecallUserRightsData>) {
        MMKVUtil.getInstance().saveLong(PreferenceConstantsInHost
                .KEY_LAST_REQUEST_NEED_SHOW_RECALL_USER_RIGHTS_DIALOG_TIME, System.currentTimeMillis())
        CommonRequestM.getRecallUserRightsData(callback)
    }

    fun showDialog(data: RecallUserRightsData) {
        val activity = BaseApplication.getTopActivity() as? MainActivity
        activity?.supportFragmentManager?.let {
            val dialog = RecallUserRightsDialog()
            dialog.setData(data)
            dialog.show(it, "RecallUserRightsDialog")
            val dialogTitle = if ("A" == data.style) "召回用户回归权益弹屏-带专辑" else "召回用户回归权益弹屏-纯图"
            // 回归权益弹屏_召回用户  弹框展示
            XMTraceApi.Trace()
                    .setMetaId(47298)
                    .setServiceId("dialogView") // 弹窗展示时上报
                    .put("currPage", "newHomePage")
                    .put("dialogTitle", dialogTitle)
                    .createTrace()
        }
    }
}

class RecallUserRightsData(jsonObject: JSONObject?) {
    var giftId: String? = null
    var landingPageUrl: String? = null
    var style: String? = null
    var styleData: StyleData? = null

    init {
        jsonObject?.apply {
            giftId = optString("giftId")
            landingPageUrl = optString("landingPageUrl")
            style = optString("style")
            optJSONObject("styleData")?.apply {
                styleData = StyleData(this)
            }
        }
    }
}

class StyleData(jsonObject: JSONObject?) {
    // a方案（专辑列表）
    var title: String? = null
    var vipTitle: String? = null
    var buttonTitle: String? = null
    var albumList: MutableList<AlbumM>? = null

    // b方案（纯图片）
    var giftCoverPath: String? = null

    init {
        jsonObject?.apply {
            if (has("title")) {
                title = optString("title")
            }
            if (has("vipTitle")) {
                vipTitle = optString("vipTitle")
            }
            if (has("buttonTitle")) {
                buttonTitle = optString("buttonTitle")
            }
            if (has("albumList")) {
                val albumArray = optJSONArray("albumList")
                if (albumArray != null && albumArray.length() > 0) {
                    albumList = ArrayList()
                    for (i in 0 until albumArray.length()) {
                        val albumStr = albumArray.optString(i)
                        if (!albumStr.isNullOrEmpty()) {
                            val albumM = AlbumM(albumStr)
                            albumList?.add(albumM)
                        }
                    }
                }
            }
            if (has("giftCoverPath")) {
                giftCoverPath = optString("giftCoverPath")
            }
        }
    }
}

data class RecallUserGiftReceiveRes(
        @SerializedName("sendResult")
        val sendResult: Boolean = false,
        @SerializedName("msg")
        val msg: String? = null
)