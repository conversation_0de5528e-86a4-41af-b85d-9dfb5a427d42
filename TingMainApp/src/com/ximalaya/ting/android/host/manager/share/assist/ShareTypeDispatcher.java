package com.ximalaya.ting.android.host.manager.share.assist;

import android.app.Activity;

import androidx.annotation.NonNull;

import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BitmapUtils;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.host.manager.share.ICustomShareContentType;
import com.ximalaya.ting.android.host.manager.share.ShareCommonUtil;
import com.ximalaya.ting.android.host.manager.share.ShareConstants;
import com.ximalaya.ting.android.host.manager.share.ShareWrapContentModel;
import com.ximalaya.ting.android.host.manager.share.model.QRShareModel;
import com.ximalaya.ting.android.host.manager.share.model.TingCircleShareModel;
import com.ximalaya.ting.android.host.manager.share.model.WeikeQRShareModel;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.shareservice.AbstractShareType;
import com.ximalaya.ting.android.shareservice.ShareModel;
import com.ximalaya.ting.android.shareservice.base.IShareDstType;

/**
 * <AUTHOR> feiwen
 * date   : 2019-08-01
 * desc   : 对不同业务的shareType作分发处理
 */
public class ShareTypeDispatcher {

    public void dispatchShareDstType(AbstractShareType shareType,
                                     ShareWrapContentModel wrapContentModel,
                                     boolean mNeedStateXdcs,
                                     @NonNull Activity activity,
                                     @NonNull IShareAssistListener.ShareDispatcherCallback shareDispatcherCallback) {
        if (mNeedStateXdcs && (wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_ALBUM
                || wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_TRACK
                || wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_ONE_KEY_PLAY
                || wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_TRACK_SALE
                || wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_USER
                || wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_VIDEO_PLAY
                || wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_FEED_VIDEO_PLAY
                || wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_WEIKE_COURSE
                || wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_WEIKE_SHARE_EARN
                || wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_SEARCH_HOT_LIST
                || wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_LOCAL_LISTEN
                || wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_AI_SOUND_CARD)) {
            UserTracking userTracking = new UserTracking();
            userTracking.setSrcModule("selectSharePlatform");
            userTracking.setItem("button");
            if (UserInfoMannage.getUid() != 0) {
                userTracking.setUserId(UserInfoMannage.getUid());
            }
            String serviceId = "pageClick";
            switch (wrapContentModel.customShareType) {
                case ICustomShareContentType.SHARE_TYPE_ALBUM:
                    userTracking.setSrcPage("album");
                    serviceId = "albumPageClick";
                    if (wrapContentModel.getAlbumModel() != null) {
                        userTracking.setSrcPageId(wrapContentModel.getAlbumModel().getId());
                        userTracking.setIsCps(wrapContentModel.getAlbumModel().isCpsProductExist());
                    }
                    break;
                case ICustomShareContentType.SHARE_TYPE_TRACK:
                case ICustomShareContentType.SHARE_TYPE_TRACK_SALE:
                    userTracking.setSrcPage("track");
                    serviceId = "trackPageClick";
                    if (wrapContentModel.soundInfo != null) {
                        userTracking.setSrcPageId(wrapContentModel.soundInfo.getDataId());
                        userTracking.setIsCps(wrapContentModel.soundInfo.isCpsProductExist());
                    }
                    break;
                case ICustomShareContentType.SHARE_TYPE_ONE_KEY_PLAY:
                    break;
                case ICustomShareContentType.SHARE_TYPE_USER:
                    userTracking.setSrcPage("user");
                    serviceId = "userPageClick";
                    userTracking.setSrcPageId(ShareModelUidHelper.getShareUid(wrapContentModel));
                    break;
                case ICustomShareContentType.SHARE_TYPE_COMMUNITY:
                    userTracking.setPostId(wrapContentModel.communityId);
                    userTracking.setSrcPage("圈子");
                    serviceId = "circlePageClick";
                    break;
                case ICustomShareContentType.SHARE_TYPE_COMMUNITY_POST:
                    userTracking.setPostId(wrapContentModel.communityArticleId);
                    userTracking.setSrcPage("帖子");
                    serviceId = "postPageClick";
                    break;
                case ICustomShareContentType.SHARE_TYPE_LIVEPERSONAL:
                    serviceId = "livePageClick";
                    if (wrapContentModel.liveId > 0) {
                        userTracking.setSrcPageId(wrapContentModel.liveId);
                    } else if (wrapContentModel.roomId > 0) {
                        userTracking.setSrcPageId(wrapContentModel.roomId);
                    }
                    break;
                case ICustomShareContentType.SHARE_TYPE_SEARCH_HOT_LIST:
                    serviceId = "searchPageClick";
                    userTracking.setSrcPage("热搜排行榜");
                    userTracking.setIsNewUser(wrapContentModel.isNewUser);
                    break;
                case ICustomShareContentType.SHARE_TYPE_LOCAL_LISTEN:
                    serviceId = XDCSCollectUtil.SERVICE_LOCAL_PAGE_CLICK;
                    userTracking.setSrcPage("localTing");
                    userTracking.setSrcPageId(wrapContentModel.localCityCode);
                    break;
                case ICustomShareContentType.SHARE_TYPE_VIDEO_PLAY:
                case ICustomShareContentType.SHARE_TYPE_FEED_VIDEO_PLAY:
                    userTracking.setSrcPage("视频页");
                    serviceId = XDCSCollectUtil.SERVICE_TRACKPAGE_CLICK;
                    boolean isLandscape = DeviceUtil.isLandscape(activity);
                    if (isLandscape) {
                        userTracking.setScreenType("landscape");
                    } else {
                        userTracking.setScreenType("portrait");
                    }
                    if (wrapContentModel.soundInfo != null) {
                        userTracking.setSrcPageId(wrapContentModel.soundInfo.getDataId());
                    }
                    break;

                //知识直播微课分享
                case ICustomShareContentType.SHARE_TYPE_WEIKE_COURSE: {

                    if (wrapContentModel.weikeLiveRoomId > 0) {//来自于直播间的分享

                        userTracking.setCourseLiveId(wrapContentModel.weikeLiveRoomId);
                        serviceId = XDCSCollectUtil.SERVICE_WEIKE_PAGE_CLICK;

                    } else {//来自于课程详情页面的分享

                        if (wrapContentModel.weikeCourseType == 1) {//单课
                            userTracking.setWeikeCourseId(wrapContentModel.weikeCourseId);
                            userTracking.setIsWeikeShareEarn(false);
                            serviceId = XDCSCollectUtil.SERVICE_WEIKE_COURSE_PAGE_CLICK;


                        } else {//系列课
                            userTracking.setWeikeCourseSetId(wrapContentModel.weikeCourseId);
                            userTracking.setIsWeikeShareEarn(false);
                            serviceId = XDCSCollectUtil.SERVICE_WEIKE_COURSE_SETPAGE_CLICK;
                        }

                    }


                    break;

                }

                //知识直播微课分享  带有返利
                case ICustomShareContentType.SHARE_TYPE_WEIKE_SHARE_EARN: {

                    //来自于课程详情页面的分享
                    if (wrapContentModel.weikeCourseType == 2) {//单课
                        userTracking.setWeikeCourseId(wrapContentModel.weikeCourseId);
                        userTracking.setIsWeikeShareEarn(true);
                        serviceId = XDCSCollectUtil.SERVICE_WEIKE_COURSE_PAGE_CLICK;


                    } else {//系列课
                        userTracking.setWeikeCourseSetId(wrapContentModel.weikeCourseId);
                        userTracking.setIsWeikeShareEarn(true);
                        serviceId = XDCSCollectUtil.SERVICE_WEIKE_COURSE_SETPAGE_CLICK;
                    }
                    break;

                }
                default:
                    break;
            }
            String albumTrackType = null;
            switch (shareType.getEnName()) {
                case IShareDstType.SHARE_TYPE_WX_CIRCLE:
                    albumTrackType = "weixinGroup";
                    break;
                case IShareDstType.SHARE_TYPE_WX_FRIEND:
                case ShareConstants.SHARE_TYPE_WX_GROUP:
                    albumTrackType = "weixin";
                    break;
                case IShareDstType.SHARE_TYPE_SINA_WB:
                    albumTrackType = "weibo";
                    break;
                case IShareDstType.SHARE_TYPE_QQ:
                    albumTrackType = "qq";
                    break;
                case IShareDstType.SHARE_TYPE_QZONE:
                    albumTrackType = "qqZone";
                    break;
                case ShareConstants.SHARE_TYPE_XM_GROUP:
                    albumTrackType = "group";
                    break;
                case ShareConstants.SHARE_TYPE_TING_CIRCLE:
                    albumTrackType = "chaos";
                    break;
                case ShareConstants.SHARE_TYPE_CREATE_QR_CODE:
                    albumTrackType = "2dCodeShare";
                    break;
                case ShareConstants.SHARE_TYPE_COMMUNITY:
                    albumTrackType = "circle";
                    break;
                case ShareConstants.SHARE_TYPE_CREATE_WEIKE_QR:
                    albumTrackType = "invitationCard";
                    break;

                case ShareConstants.SHARE_TYPE_LINK:
                    albumTrackType = "copylink";
                    break;
                case ShareConstants.SHARE_TYPE_MORE:
                    albumTrackType = "more";
                    break;
                default:
                    break;
            }
            if (albumTrackType != null) {
                userTracking.setItemId(albumTrackType).
                        statIting(XDCSCollectUtil.APP_NAME_EVENT, serviceId);
            }
        }
        if (ShareConstants.SHARE_TYPE_TING_CIRCLE.equals(shareType.getEnName())) {
            //  2017/9/8  听友圈直接跳转 不用请求网络
            TingCircleShareModel model = new TingCircleShareModel();
            model.albumModel = wrapContentModel.getAlbumModel();
            model.soundInfo = wrapContentModel.soundInfo;
            model.customShareType = wrapContentModel.customShareType;
            model.simpleShareData = wrapContentModel.simpleShareData;
            model.mcShareData = wrapContentModel.mcShareData;
            model.liveId = wrapContentModel.liveId;
            model.roomId = wrapContentModel.roomId;
            model.anchorUid = wrapContentModel.anchorUid;
            model.albumListenNote = wrapContentModel.albumListenNote;
            shareDispatcherCallback.onShare(shareType, model);
            ShareTrackHelper.setXMGroupTrack(wrapContentModel);
        } else if (ShareConstants.SHARE_TYPE_CREATE_QR_CODE.equals(shareType.getEnName())) {
            Track track=wrapContentModel.soundInfo;
            QRShareModel qrShareModel = wrapContent2QrShareModel(wrapContentModel);
            // 跳转到生成二维码页面 不用请求网络 直接发起分享
            shareDispatcherCallback.onShare(shareType, qrShareModel);
        } else if (ShareConstants.SHARE_TYPE_CREATE_WEIKE_QR.equals(shareType.getEnName())) {//微课 二维码海报

            WeikeQRShareModel model = new WeikeQRShareModel();
            model.courseId = wrapContentModel.weikeCourseId;
            model.courseType = wrapContentModel.weikeCourseType;
            model.shareType = wrapContentModel.customShareType;
            model.coverUrl = wrapContentModel.weikeCover;
            shareDispatcherCallback.onShare(shareType, model);
        } else if (ShareConstants.SHARE_TYPE_ADD_TO_DESKTOP.equals(shareType.getEnName())) {
            // 填到到桌面快捷方式，不用请求网络
            ShareModel addToDesktopModel = new ShareModel();
            if (ICustomShareContentType.SHARE_TYPE_ALBUM == wrapContentModel.customShareType) {
                AlbumM albumM = wrapContentModel.getAlbumModel();
                if (albumM != null) {
                    addToDesktopModel.setId(albumM.getId());
                    addToDesktopModel.setTitle(albumM.getAlbumTitle());
                    addToDesktopModel.setContent(albumM.getValidCover());

                }
            } else if (ICustomShareContentType.SHARE_TYPE_RADIO == wrapContentModel.customShareType) {
                addToDesktopModel.setId(wrapContentModel.radioId);
                addToDesktopModel.setTitle(wrapContentModel.title);
                addToDesktopModel.setContent(wrapContentModel.picUrl);
            }
            addToDesktopModel.setType(wrapContentModel.customShareType);

            shareDispatcherCallback.onShare(shareType, addToDesktopModel);
        } else if (ShareConstants.SHARE_TYPE_SHORT_CONTENT.equals(shareType.getEnName())) {
            // 本地跳转页面 因此 没必要进行下一步操作
            // shareDispatcherCallback.onShare(shareType, null);
        } else {
            if (ICustomShareContentType.SHARE_TYPE_AI_SOUND_CARD == wrapContentModel.customShareType) {
                // 爸妈讲故事分享
                ShareModel.WXShareModel mShareModel = new ShareModel.WXShareModel(wrapContentModel.aiShareType, ShareModel.SHARE_CONTETN_WEB);
                mShareModel.setTitle(wrapContentModel.title);
                mShareModel.setDescription(wrapContentModel.aiSoundContent);
                mShareModel.setThumbData(BitmapUtils.bitmap2Bytes(wrapContentModel.bitmap));
                mShareModel.setWebpageUrl(wrapContentModel.aiSoundShareUrl);
                mShareModel.setType(wrapContentModel.customShareType);
                shareDispatcherCallback.onShare(shareType, mShareModel);
            } else {
                shareDispatcherCallback.onRequestGetShareContent(wrapContentModel);
            }
        }
    }

    public static QRShareModel wrapContent2QrShareModel(ShareWrapContentModel wrapContentModel) {
        QRShareModel qrShareModel = new QRShareModel();
        // 海报专用
        if (ShareCommonUtil.INSTANCE.usePodCast(wrapContentModel)) {
            qrShareModel.pageIndex = wrapContentModel.posterPageIndex;
            qrShareModel.customPosterType = wrapContentModel.customPosterType;
            qrShareModel.actPosterData = wrapContentModel.actPosterData;
            qrShareModel.goldenSentence = wrapContentModel.goldenSentenceData;
            qrShareModel.mVipGiftCardInfoModel = wrapContentModel.mVipGiftCardInfoModel;
            if (wrapContentModel.soundInfo != null) {
                qrShareModel.picUrl = wrapContentModel.soundInfo.getValidCover();
                qrShareModel.title = wrapContentModel.soundInfo.getTrackTitle();
            }
            if (wrapContentModel.getAlbumModel() != null) {
                qrShareModel.picUrl = wrapContentModel.getAlbumModel().getValidCover();
                qrShareModel.title = wrapContentModel.getAlbumModel().getAlbumTitle();
            }
        }
        if (wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_MY_ALBUM
                || wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_MY_TRACK) {
            if (wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_MY_ALBUM) {
                if (wrapContentModel.getAlbumModel() != null) {
                    qrShareModel.id = wrapContentModel.getAlbumModel().getId();
                    qrShareModel.type = wrapContentModel.getAlbumModel().isPaid()
                            ? IMainFunctionAction.FragmentConstants.TYPE_PAID_ALBUM
                            : IMainFunctionAction.FragmentConstants.TYPE_FREE_ALBUM;
                    if (wrapContentModel.specialSubType > 0) {
                        qrShareModel.shareSubType = wrapContentModel.specialSubType;
                    }
                }
            } else /*if (wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_MY_TRACK)*/ {
                if (wrapContentModel.soundInfo != null) {
                    qrShareModel.id = wrapContentModel.soundInfo.getDataId();
                    qrShareModel.picUrl = wrapContentModel.soundInfo.getValidCover();
                    qrShareModel.type = wrapContentModel.soundInfo.isPaid()
                            ? IMainFunctionAction.FragmentConstants.TYPE_PAID_TRACK
                            : IMainFunctionAction.FragmentConstants.TYPE_FREE_TRACK;
                    qrShareModel.shareSubType = wrapContentModel.paramSubType;
                }
            }
        } else {
            if (wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_USER) {
                qrShareModel.id = ShareModelUidHelper.getShareUid(wrapContentModel);
                qrShareModel.type = IMainFunctionAction.FragmentConstants.TYPE_ANCHOR;
            } else if (wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_ALBUM
                    || wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_ALBUM_EARN) {
                if (wrapContentModel.getAlbumModel() != null) {
                    boolean isSelectFirst = false;
                    if (wrapContentModel.getAlbumModel().getCategoryId() == 3
                            || wrapContentModel.getAlbumModel().getId() == 11549955) {
                        isSelectFirst = true;
                    }
                    qrShareModel.isSelectFirst = isSelectFirst;
                    qrShareModel.id = wrapContentModel.getAlbumModel().getId();
                    qrShareModel.type = wrapContentModel.getAlbumModel().isPaid()
                            ? IMainFunctionAction.FragmentConstants.TYPE_PAID_ALBUM
                            : IMainFunctionAction.FragmentConstants.TYPE_FREE_ALBUM;
                }

                if (wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_ALBUM_EARN) {
                    qrShareModel.isCps = true;
                }
                if (wrapContentModel.specialSubType > 0) {
                    qrShareModel.shareSubType = wrapContentModel.specialSubType;
                }
            } else if (wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_TRACK
                    || wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_ONE_KEY_PLAY
                    ||wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_TRACK_SALE) {
                if (wrapContentModel.soundInfo != null) {
                    boolean isSelectFirst = false;
                    if (wrapContentModel.soundInfo.getCategoryId() == 3
                            || wrapContentModel.soundInfo.getAlbum() != null && wrapContentModel.soundInfo.getAlbum().getAlbumId() == 11549955) {
                        isSelectFirst = true;
                    }
                    qrShareModel.isSelectFirst = isSelectFirst;
                    qrShareModel.id = wrapContentModel.soundInfo.getDataId();
                    qrShareModel.picUrl = wrapContentModel.soundInfo.getValidCover();
                    qrShareModel.type = wrapContentModel.soundInfo.isPaid()
                            ? IMainFunctionAction.FragmentConstants.TYPE_PAID_TRACK
                            : IMainFunctionAction.FragmentConstants.TYPE_FREE_TRACK;
                    qrShareModel.sharePoint = wrapContentModel.sharePoint;
                    qrShareModel.shareSubType = wrapContentModel.paramSubType;
                }
            } else if (wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_VIDEO_PLAY) {
                if (wrapContentModel.soundInfo != null) {
                    qrShareModel.id = wrapContentModel.soundInfo.getDataId();
                    qrShareModel.type = wrapContentModel.soundInfo.isPaid()
                            ? IMainFunctionAction.FragmentConstants.TYPE_PAID_VIDEO
                            : IMainFunctionAction.FragmentConstants.TYPE_FREE_VIDEO;
                }
            } else if (wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_COMMUNITY) {
                qrShareModel.id = wrapContentModel.communityId;
                qrShareModel.type = IMainFunctionAction.FragmentConstants.TYPE_COMMUNITY;
            } else if (wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_COMMUNITY_POST) {
                qrShareModel.id = wrapContentModel.communityId;
                qrShareModel.articleId = wrapContentModel.communityArticleId;
                qrShareModel.type = IMainFunctionAction.FragmentConstants.TYPE_COMMUNITY_ARTICLE;
            }else if(wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_TRACK_DUB){
                qrShareModel.id=wrapContentModel.trackId;
                qrShareModel.picUrl=wrapContentModel.picUrl;
                qrShareModel.dubRoleNames=wrapContentModel.roleNames;
                qrShareModel.isDubVideoShare=true;
                qrShareModel.ipTagName=wrapContentModel.ipTagName;
                qrShareModel.isInvitedDub=wrapContentModel.isInvitedDub;
                qrShareModel.fromPage=wrapContentModel.fromPage;
                qrShareModel.type=IMainFunctionAction.FragmentConstants.TYPE_DUB_VIDEO;
            }else if (wrapContentModel.customShareType==ICustomShareContentType.SHARE_TYPE_TRACK_DUB_NEW ||
                    wrapContentModel.customShareType==ICustomShareContentType.SHARE_TYPE_NEW_TOPIC_SIMPLE_WORK ){
                qrShareModel.id=wrapContentModel.trackId;
                qrShareModel.picUrl=wrapContentModel.picUrl;
                qrShareModel.dubRoleNames=wrapContentModel.roleNames;
                qrShareModel.ipTagName=wrapContentModel.ipTagName;
                qrShareModel.anchorName=wrapContentModel.anchorUsername;
                qrShareModel.anchorAvatarUrl=wrapContentModel.anchorUserAvatar;
                qrShareModel.isDubVideoShare=true;
                qrShareModel.isInvitedDub=wrapContentModel.isInvitedDub;
                qrShareModel.isDubbingInfoPage=wrapContentModel.isDubbingInfoPage;
                qrShareModel.fromPage=wrapContentModel.fromPage;
                qrShareModel.type=IMainFunctionAction.FragmentConstants.TYPE_DUB_VIDEO;
            }else if(wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_LIVEPERSONAL
                    || wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_LIVE_ENT ||
                    wrapContentModel.customShareType == ICustomShareContentType.SHARE_TYPE_LIVE_VIDEO){
                qrShareModel.type=IMainFunctionAction.FragmentConstants.TYPE_LIVE_ROOM;
                if(ConstantsOpenSdk.isDebug && wrapContentModel.roomId <= 0){
                    throw new RuntimeException("直播间分享参数错误 roomId 为 0 ");
                }
                switch (wrapContentModel.customShareType){
                    case ICustomShareContentType.SHARE_TYPE_LIVEPERSONAL:
                        qrShareModel.shareSubType = BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_AUDIO;
                        qrShareModel.id = wrapContentModel.liveId;
                        qrShareModel.anchorUid = wrapContentModel.anchorUid;
                        break;
                    case ICustomShareContentType.SHARE_TYPE_LIVE_ENT:
                        qrShareModel.shareSubType = BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_PGC;
                        qrShareModel.id = wrapContentModel.roomId;
                        qrShareModel.anchorUid = wrapContentModel.anchorUid;
                        break;
                    case ICustomShareContentType.SHARE_TYPE_LIVE_VIDEO:
                        qrShareModel.shareSubType = BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_COURSE;
                        qrShareModel.id = wrapContentModel.liveId;
                        qrShareModel.anchorUid = wrapContentModel.anchorUid;
                        break;
                }
            }
        }
        return qrShareModel;
    }
}
