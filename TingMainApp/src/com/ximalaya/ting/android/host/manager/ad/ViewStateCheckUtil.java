package com.ximalaya.ting.android.host.manager.ad;

import android.content.Context;
import android.graphics.Rect;
import android.os.Build;
import android.os.PowerManager;
import android.view.View;

import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.xmutil.SystemServiceManager;


/**
 * Created by le.xin on 2021/1/22.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class ViewStateCheckUtil {
    private static final Rect rect = new Rect();
    private static boolean globalVisable(View view, int percent) {
        if (view != null && view.getVisibility() == View.VISIBLE && view.getParent() != null) {
            if (!view.getGlobalVisibleRect(rect)) {
                return false;
            } else {
                long var3 = (long) rect.height() * (long) rect.width();
                long var5 = (long) view.getHeight() * (long) view.getWidth();
                return var5 > 0L && 100L * var3 >= (long) percent * var5;
            }
        } else {
            return false;
        }
    }

    private static boolean isScreenOn(Context context) throws Throwable {
        PowerManager manager = SystemServiceManager.getPowerManager(ToolUtil.getCtx());

        boolean isScreenOn = false;
        if (manager != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT_WATCH) {
                isScreenOn = manager.isInteractive();
            } else {
                isScreenOn = manager.isScreenOn();
            }
        }

        return isScreenOn;
    }

    private static boolean isShown(View view) {
        return view != null && view.isShown();
//        return view != null && view.getVisibility() == View.VISIBLE;
    }

    private static int isVisibility(View view, int percent) throws Throwable {
        int visableStyle = 0;
        if (!isScreenOn(view.getContext())) { // 是否开屏
            visableStyle = 4;
        } else if (!isShown(view)) {        // 是否shown
            visableStyle = 1;
        } else if (!globalVisable(view, percent)) { // 按百分比可见检测
            visableStyle = 3;
        }

        return visableStyle;
    }

    /**
     * 检查是否可视
     * @param view
     * @param percent 超过这个百分比才认为这个展示了
     * @return
     */
    public static boolean checkIsVisibility(View view, int percent) {
        try {
            return 0 == isVisibility(view, percent);
        } catch (Throwable var4) {
            return false;
        }
    }

}
