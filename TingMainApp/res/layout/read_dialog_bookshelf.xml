<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="275dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/read_solid_color_white_corner_10dp">

        <ImageView
            android:id="@+id/iv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="13dp"
            android:src="@drawable/read_ic_close"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="12dp"
            android:gravity="center"
            android:text="历史阅读记录可在\n【我的】 - 【小说书城】中找到哦 "
            android:textColor="@color/host_color_333333_dcdcdc"
            android:textSize="16dp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_cancel" />

        <ImageView
            android:id="@+id/iv_logo"
            android:layout_width="231dp"
            android:layout_height="178dp"
            android:layout_marginTop="14dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_title" />

        <View
            android:id="@+id/line"
            android:layout_width="0dp"
            android:layout_height="0.5dp"
            android:background="@color/host_color_e8e8e8_353535"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_logo" />

        <TextView
            android:id="@+id/tv_left"
            android:layout_width="0dp"
            android:layout_height="45dp"
            android:background="@drawable/read_dialog_left_bt_bg"
            android:gravity="center"
            android:text="退出"
            android:textColor="@color/host_color_666666_8d8d91"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/tv_right"
            app:layout_constraintTop_toBottomOf="@+id/line" />


        <TextView
            android:id="@+id/tv_right"
            android:layout_width="0dp"
            android:layout_height="45dp"
            android:background="@drawable/read_dialog_right_bt_bg"
            android:gravity="center"
            android:text="加入书架"
            android:textColor="@color/host_color_white_ffffff"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/tv_left"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/line" />


    </androidx.constraintlayout.widget.ConstraintLayout>


</FrameLayout>