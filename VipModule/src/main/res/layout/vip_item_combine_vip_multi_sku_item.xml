<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="130dp"
    android:layout_height="175dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:clipChildren="false"
    android:clipToPadding="false"
    tools:background="#000000"
    >

<!--    未选中的背景-->
    <ImageView
        android:id="@+id/vip_purchase_dialog_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/vip_bg_vip_dialog_combine"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginTop="5dp"
        app:layout_constraintRight_toRightOf="parent" />

    <!--    选中以后的背景-->
    <View
        android:id="@+id/vip_purchase_dialog_multi_mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/vip_bg_vip_dialog_combine_selected"
        android:visibility="gone"
        android:layout_marginTop="5dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:visibility="gone" />

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:cardCornerRadius="8dp"
        app:cardElevation="0dp"
        android:layout_marginTop="5dp"
        app:cardBackgroundColor="@color/host_transparent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        >

        <ImageView
            android:id="@+id/vip_purchase_dialog_bg_skin"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            android:scaleType="centerCrop"
            tools:visibility="visible" />

    </androidx.cardview.widget.CardView>

    <include
        layout="@layout/vip_view_time_limited_discount_value_new"
        android:layout_width="44dp"
        android:layout_height="44dp"
        android:layout_marginTop="5dp"
        android:visibility="invisible"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/vip_purchase_item_tag_high_priority_container"
        android:layout_width="wrap_content"
        android:layout_height="18dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="1dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="@drawable/vip_bg_rect_ffff3e4e_fffe9449_radius_4_4_0_4">

        <ImageView
            android:id="@+id/vip_purchase_item_tag_high_priority_icon"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginVertical="4dp"
            android:layout_marginLeft="3dp"
            tools:visibility="gone"
            tools:src="@drawable/vip_ic_vip_logo_small" />

        <TextView
            android:id="@+id/vip_purchase_item_tag_high_priority"
            android:layout_width="wrap_content"
            android:layout_height="16dp"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxWidth="91dp"
            android:maxLines="1"
            android:paddingLeft="3dp"
            android:paddingRight="4dp"
            android:textColor="@color/host_color_ffffff"
            android:textSize="10dp"
            android:visibility="gone"
            tools:text="限时优惠"
            tools:visibility="gone" />
    </LinearLayout>


    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="24dp"
        app:cardCornerRadius="4dp"
        app:cardElevation="0dp"
        app:cardBackgroundColor="@color/host_transparent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible"
        app:layout_constraintLeft_toLeftOf="parent">

        <ImageView
            android:id="@+id/vip_purchase_item_tag_low_priority"
            android:layout_width="wrap_content"
            android:layout_height="18dp"
            android:maxWidth="91dp"
            android:gravity="center_vertical"
            android:scaleType="fitStart"
            android:visibility="gone"
            tools:visibility="visible"
            tools:src="@drawable/vip_icon_svip_tag"
            tools:background="#666666"/>

    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/vip_purchase_item_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textSize="16dp"
        android:textColor="@color/host_color_333333_cfcfcf"
        android:textFontWeight="400"
        android:gravity="center"
        android:maxLines="1"
        android:ellipsize="end"
        android:paddingLeft="@dimen/host_x12"
        android:paddingRight="@dimen/host_x12"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/vip_price_ll"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="连续包月" />

    <LinearLayout
        android:id="@+id/vip_price_ll"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="bottom|center_horizontal"
        android:layout_marginLeft="2dp"
        android:layout_marginRight="2dp"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@+id/vip_purchase_item_name"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/vip_purchase_item_origin_price"
        app:layout_constraintRight_toRightOf="parent">

        <TextView
            android:id="@+id/vip_purchase_item_now_price_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="￥"
            android:textSize="20dp"
            android:textStyle="bold"
            android:includeFontPadding="false"
            android:textColor="@color/vip_color_fffa5f42" />

        <com.ximalaya.ting.android.vip.view.VipRunNumberTextView
            android:id="@+id/vip_purchase_item_now_price_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textColor="@color/vip_color_fffa5f42"
            android:textSize="40dp"
            android:textStyle="bold"
            android:visibility="visible"
            app:vip_rnt_auto="false"
            app:vip_rnt_decimal_text_size="20sp"
            app:vip_rnt_duration="1150"
            tools:text="20"
            tools:visibility="visible" />


        <TextView
            android:id="@+id/vip_purchase_item_now_price_suffix"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:text="起"
            android:textColor="#292B33"
            tools:textColor="@color/host_color_white_90"
            android:textSize="18dp"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_marginLeft="2dp"
            android:textFontWeight="400"/>

    </LinearLayout>

    <TextView
        android:id="@+id/vip_purchase_item_origin_price"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textSize="12dp"
        android:textColor="#9f9f9f"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center|top"
        android:visibility="gone"
        android:paddingLeft="3dp"
        android:paddingRight="3dp"
        android:paddingBottom="8dp"
        android:alpha="0.5"
        app:layout_constraintTop_toBottomOf="@+id/vip_price_ll"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:visibility="invisible"
        tools:text="￥498" />

    <FrameLayout
        android:layout_width="0dp"
        android:layout_height="30dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <View
            android:id="@+id/vip_purchase_item_selling_point_bg"
            android:layout_width="match_parent"
            android:layout_gravity="bottom"
            android:layout_height="30dp" />

        <TextView
            android:id="@+id/vip_purchase_item_description"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:ellipsize="end"
            android:gravity="center"
            android:layout_gravity="bottom"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/host_x12"
            android:textColor="@color/host_color_regularTextColor"
            android:textSize="14dp"
            android:visibility="gone"
            tools:background="#f6f5f5"
            tools:text="次月续费¥20"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/vip_purchase_item_promotion"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:ellipsize="end"
            android:layout_gravity="bottom"
            android:gravity="center"
            android:maxLines="1"
            android:paddingLeft="@dimen/host_x12"
            android:paddingRight="@dimen/host_x12"
            android:textColor="@color/host_color_regularTextColor"
            android:textSize="14dp"
            android:visibility="gone"
            tools:background="@drawable/vip_bg_bottom_sku_limit_tip"
            tools:text="用券已减14元"
            tools:visibility="visible" />
    </FrameLayout>




    <com.ximalaya.ting.android.host.view.XmLottieAnimationView
        android:id="@+id/main_iv_red_packet_lottie"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:scaleType="centerCrop"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="5dp"
        app:lottie_autoPlay="false"
        app:lottie_repeatCount="0"
        app:lottie_fileName="lottie/redpacket.json" />

</androidx.constraintlayout.widget.ConstraintLayout>