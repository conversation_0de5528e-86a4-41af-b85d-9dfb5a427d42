package com.ximalaya.ting.android.live.host.fragment.customextend;

import com.ximalaya.ting.android.live.lib.chatroom.entity.custom.CustomAttentionMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.custom.CustomEnterRoomMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonGoodsInfoChangedMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonGoodsOrderChangedMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.custom.CustomIncrementUserMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.custom.CustomLiveStatusChangeMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.custom.CustomOnlineCountMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.custom.CustomOperateMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.custom.CustomPublishTopicMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.custom.CustomShareLiveRoomMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.custom.CustomShutUpMessage;

/**
 * 通用的业务相关的消息回调
 * 在线人数1
 * 直播状态变化2
 * 进场通知 3
 * 房间禁言 4
 * 添加管理员 5
 * 移除管理员 6
 * 分享直播间 8
 * 关注主播 9
 * 发布话题 10
 * 增量推送用户 11
 * <p>
 * 最新Type 请对比：
 * http://gitlab.ximalaya.com/xmc-team/xmc-guide/blob/master/LiveChatServer/rmc-chat-system-msg.md#%E5%9C%A8%E7%BA%BF%E4%BA%BA%E6%95%B0
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18202765712
 */
public interface ICustomMessageReceived {
    /**
     * 在线人数状态变化
     *
     * @param onlineCountMessage
     */
    void onReceiveOnlineStatusMessage(CustomOnlineCountMessage onlineCountMessage);

    /**
     * 直播状态变化
     *
     * @param roomStatusChangeMessage
     */
    void onReceiveRoomStatusChangeMessage(CustomLiveStatusChangeMessage roomStatusChangeMessage);

    /**
     * 入场通知
     *
     * @param enterRoomMessage
     */
    void onReceiveEnterRoomMessage(CustomEnterRoomMessage enterRoomMessage);

    /**
     * 禁言，解禁消息
     *
     * @param shutUpMessage
     */
    void onReceiveShutUpMessage(CustomShutUpMessage shutUpMessage);

    /**
     * 添加/删除管理员
     *
     * @param operateMessage
     */
    void onReceiveOperateMessage(CustomOperateMessage operateMessage);

    /**
     * 房间分享消息
     *
     * @param shareLiveRoomMessage
     */
    void onReceiveShareRoomMessage(CustomShareLiveRoomMessage shareLiveRoomMessage);

    /**
     * 关注用户的通知消息
     *
     * @param attentionMessage
     */
    void onReceiveAttentionMessage(CustomAttentionMessage attentionMessage);

    /**
     * 话题发布消息
     *
     * @param publishTopicMessage
     */
    void onReceivePublishTopicMessage(CustomPublishTopicMessage publishTopicMessage);

    /**
     * 推送增量用户消息
     *
     * @param incrementUserMessage
     */
    void onReceiveIncrementUserMessage(CustomIncrementUserMessage incrementUserMessage);

    /**
     * 商品信息变更
     * @param goodsInfoChangedMessage
     */
    void onReceiveGoodsInfoChangedMessage(CommonGoodsInfoChangedMessage goodsInfoChangedMessage);

    /**
     * 商品顺序变更
     * @param goodsOrderChangedMessage
     */
    void onReceiveGoodsOrderChangedMessage(CommonGoodsOrderChangedMessage goodsOrderChangedMessage);
}
