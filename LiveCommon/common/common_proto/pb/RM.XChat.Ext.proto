syntax="proto2";
package RM.XChat;

import "RM.Base.proto";
import "RM.Base.Ext.proto";

option objc_class_prefix = "RMXChat";

message VersionUpdataTips
{
    optional string content = 1;
}

message VersionUpdataForPattern
{
    optional string content = 1;
    required int32 patternType = 2;
}

message GiftMsg
{
    required uint64 chatId = 1;
    required RM.Base.UserInfo userInfo = 2;
    required string conseUnifiedNo = 3;
    required uint64 giftId = 4;
    required uint64 quantity = 5;
    required uint64 hits = 6;
    required int32 duration = 7;
    required uint64 sendTime = 8;
    optional uint64 innerValue = 9;
    optional int32 nobleTemplateId = 10;
    map<string, string> extData = 11;
    optional string orderNo = 12;
    repeated RM.Base.AnimateReplace msgReplace = 13;
}

message GiftMsgSp
{
    required uint64 chatId = 1;
    required RM.Base.UserInfo userInfo = 2;
    required string conseUnifiedNo = 3;
    required uint64 giftId = 4;
    required uint64 quantity = 5;
    required uint64 hits = 6;
    required int32 duration = 7;
    required uint64 sendTime = 8;
    required string content = 9;
    required string giftName = 10;
}

message GiftComboOver
{
    required uint64 chatId = 1;
    required RM.Base.UserInfo userInfo = 2;
    required string conseUnifiedNo = 3;
    required uint64 giftId = 4;
    required uint64 totalQuantity = 5;
    required uint64 sendTime = 6;
}

message RedPacket
{
    required uint64 chatId = 1;
    required RM.Base.UserInfo userInfo = 2;
    required uint64 redPacketId = 3;
    required string content = 4;
    optional bool isShowContent = 5;
    optional int32 templateId = 6;
    optional uint64 startTime = 7;
    optional string newContent = 8;
    optional bool superPacket = 9;
    optional int32 amount = 10;
}

message TimedRedPacket
{
    required uint64 chatId = 1;
    required uint64 redPacketId = 2;
    required uint64 startTime = 3;
    required uint64 totalTime = 4;
    required uint64 timestamp = 5;
    required bool isShowContent = 6;
    optional string content = 7;
    optional RM.Base.UserInfo userInfo = 8;
    optional int32 templateId = 9;
    optional string newContent = 10;
    optional bool superPacket = 11;
    optional int32 amount = 12;
}

message FollowerRedPacket
{
    required uint64 chatId = 1;
    required uint64 redPacketId = 2;
    required uint64 startTime = 3;
    required uint64 totalTime = 4;
    required uint64 timestamp = 5;
    required bool isShowContent = 6;
    optional string content = 7;
    optional RM.Base.UserInfo userInfo = 8;
    optional int32 templateId = 9;
    optional string newContent = 10;
    optional bool superPacket = 11;
    optional int32 amount = 12;
}

message GeneralRedPacket
{
    required uint64 chatId = 1;
    required uint64 redPacketId = 2;
    required uint64 startTime = 3;
    required uint64 totalTime = 4;
    required uint64 timestamp = 5;
    required bool isShowContent = 6;
    optional string content = 7;
    optional RM.Base.UserInfo userInfo = 8;
    required int32 redPacketType = 9;
    optional string redPacketContent = 10;
    optional int32 templateId = 11;
    optional string newContent = 12;
    optional bool superPacket = 13;
    optional int32 amount = 14;
}

message GetRedPacket
{
    required uint64 chatId = 1;
    required RM.Base.UserInfo userInfo = 2;
    required string content = 3;
    optional RM.Base.UserInfo atUserInfo = 4;
    optional string newContent = 5;
    optional int32 type = 6;
}

message ShareLiveRoom
{
    required uint64 chatId = 1;
    required RM.Base.UserInfo userInfo = 2;
    required string content = 3;
}

message UserJoin
{
    required uint64 chatId = 1;
    required RM.Base.UserInfo userInfo = 2;
    required string content = 3;
    optional int32 styleType = 4;
    optional bool isNoble = 5;
    optional int32 animatedStyleType = 6;
    optional bool freshFlag = 7;
    optional int32 priority = 8;
    optional int32 type = 9;
    optional int32 grade = 10;
    optional string name = 11;
    optional uint64 templateId = 12;
    optional string animatedContent = 13;
    optional bool welcomeFreshFlag = 14;
    optional int32 smallStyleType = 15; //小型进场动效
    repeated RM.Base.NormalStyle styles = 16; //普通特效
}

message AnchorMsg
{
    required uint64 chatId = 1;
    required RM.Base.UserInfo userInfo = 2;
    required string content = 3;
    // 新增 type 字段用于区分消息类型
    optional int32 type = 4;
    // 新增目标用户信息
    optional RM.Base.UserInfo atUserInfo = 5;
}

message SystemMsg
{
    required uint64 chatId = 1;
    required int32 type = 2;
    required string content = 3;
}

message AudienceMsg
{
    required uint64 chatId = 1;
    required RM.Base.UserInfo userInfo = 2;
    required string content = 3;
    optional int32 type = 4;
    optional int32 newType = 5;
    optional RM.Base.UserInfo atUserInfo = 6;
    optional string newContent = 7;
}

message UserInfoUpdate
{
    required uint64 userId = 1;
}

message GiftBoxMsg
{
    required uint64 giftBoxId = 1;
    required bool prize = 2;
    required uint64 chatId = 3;
    optional RM.Base.UserInfo userInfo = 4;
    optional RM.Base.UserInfo receiverInfo = 5;
    optional uint64 giftId = 7;
    optional uint64 quantity = 8;
    optional uint64 sendTime = 9;
    optional uint64 innerValue = 10;
    optional int32 nobleTemplateId = 11;
}

message GiftLotMsg
{
    required uint64 giftLotId = 1;
    optional uint64 giftLotQuantity = 2;
    optional RM.Base.UserInfo senderInfo = 3;
    optional RM.Base.UserInfo receiverInfo = 4;
    optional uint64 giftId = 5;
    optional uint64 giftQuantity = 6;
    optional uint64 sendTime = 7;
    optional uint64 innerValue = 8;
    optional int32 nobleTemplateId = 9;
}

message KickUser
{
    required int32 resultCode = 1;
    optional string content = 2;
    optional uint64 userId = 3;
}

message QueryRoomModeReq
{
    required uint64 roomId = 2;
}

message QueryRoomModeRsp
{
    required int32 resultCode = 1;
    required uint64 roomId = 2;
    required int32 mode = 3;
    optional uint64 timeStamp = 4;
    repeated int32 modeList = 5;
    repeated RM.Base.ModeStatus modeStatus = 6;
}

message FloatScreen
{
    required int32 type = 1;
    optional string content = 2;
    optional RM.Base.MsgValidityLimit msgValidityLimit = 3;
}

message RoomStatusReq
{
    optional uint64 uniqueId = 1;
}

message RoomStatusRsp
{
    optional uint64 userCnt = 1;
    optional uint64 uniqueId = 2;
    optional uint64 hotValue = 3;
}

message WelcomeMsg
{
    required RM.Base.UserInfo anchorUser = 1;
    optional uint64 userId = 2;
    optional string nickname = 3;
    optional string beforeContent = 4;
    optional string afterContent = 5;
    optional bool automatic = 6;    //true:自动；false:手动
}

message WithdrawChatMsg {
    required uint64 msgId = 1;
    optional int32 withdrawType = 2 [default=0];
    optional uint64 userId = 3;
    optional uint64 timeStamp = 4;
}

message CdnStatusMsg {
    required int32 statusType = 1;    //cdn状态，具体见枚举CdnStatusType
    optional string extend = 2;
}

message DoubleClickReq {
    optional uint64 uniqueId = 1;
    required int32 clickType = 2;
}

message DoubleClickRsp {
    required int32 resultCode = 1;
    optional string reason = 2;
    optional uint64 uniqueId = 3;
}

message DoubleClickMsg {
    repeated RM.Base.DoubleClickModel clickModels = 1;
}

message GiftBoxListMsg
{
    required int32 giftBoxId = 1;
    required uint64 chatId = 2;
    optional RM.Base.UserInfo sender = 3;
    optional uint64 sendTime = 4;
    optional int32 nobleTemplateId = 5;
    optional int32 quantity = 6;
    // 按照价格排序
    repeated RM.Base.GiftBoxListModel boxList = 7;
    optional int32 templateId = 8;
    optional string boxReceiverKey = 9; // 礼盒接收者key
    optional int32 micCnt = 10; //收礼主播数
    optional int32 openGiftSplitType = 11; //boxList拆分模式，1按uid/2按照麦位
}

