package com.ximalaya.ting.android.live.common.lib.entity.rank;

import android.text.TextUtils;

import androidx.annotation.Keep;

/**
 * 直播间在线用户实体类
 *
 * @Date 2024/1/18
 * @Email <EMAIL>
 * <AUTHOR>
 */
@Keep
public class CommonRoomOnlineUserModel {

//     "nickname": "",
//             "uid": 0,
//             "avatarPath": "",
//             "rank": 0,
//             "contribution": 0,
//             "isVerified": false,
//             "invisible": false,
//             "guardFlag": false,
//             "guardGodFlag": false


    public long uid = 0;

    public String avatarPath;

    public int rank;

    public long contribution;

    public String nickname;

    public boolean isVerified;

    /**
     * 是否贵族隐身
     */
    public boolean invisible = false;

    /**
     * 短链消息里，判断是否是守护的字段
     **/
    public boolean guardFlag;

    /**
     * 在线列表接口，返回不为空的是守护
     **/
    public String guardMedalPath;


    /**
     * 是否是守护团用户
     *
     * @return true 守护团用户 false 非守护团用户
     */
    public boolean isGuardUser() {
        return false;
    }

    /**
     * 贵族隐身用户显示神秘人，其他显示昵称
     *
     * @return 返回昵称或者神秘人
     */
    public String adaptNicknameByNobleInVisible() {
        if (invisible) {
            return "神秘人";
        }

        if (TextUtils.isEmpty(nickname)) {
            return "";
        }

        return nickname;
    }

}
