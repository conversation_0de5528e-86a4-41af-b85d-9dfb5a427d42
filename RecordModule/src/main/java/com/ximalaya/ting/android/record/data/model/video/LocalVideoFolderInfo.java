package com.ximalaya.ting.android.record.data.model.video;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ervin.li on 2018/5/28.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class LocalVideoFolderInfo {


    private int folderId;
    private String folderName;
    private List<LocalVideoInfo> videoList;

    public void setFolderId(int folderId) {
        this.folderId = folderId;
    }

    public void setFolderName(String folderName) {
        this.folderName = folderName;
    }

    public void setVideoList(ArrayList<LocalVideoInfo> videoList) {
        this.videoList = videoList;
    }

    public int getFolderId() {
        return folderId;
    }

    public String getFolderName() {
        return folderName;
    }

    public List<LocalVideoInfo> getVideoList() {
        return videoList;
    }
}
