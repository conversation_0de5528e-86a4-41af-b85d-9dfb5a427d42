package com.ximalaya.ting.android.record.fragment.prog;

import android.content.Context;
import android.os.Bundle;
import android.text.Editable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextWatcher;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.handmark.pulltorefresh.library.PullToRefreshBase;
import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.util.IDbDataCallBack;
import com.ximalaya.ting.android.record.R;
import com.ximalaya.ting.android.record.adapter.prog.SingleSelectAdapter;
import com.ximalaya.ting.android.record.constants.StatusType;
import com.ximalaya.ting.android.record.constants.VideoType;
import com.ximalaya.ting.android.record.data.model.record.Record;
import com.ximalaya.ting.android.record.fragment.dub.wrapper.EditTextWatcherWrapper;
import com.ximalaya.ting.android.record.fragment.dub.wrapper.OnEditorActionListenerWrapper;
import com.ximalaya.ting.android.record.fragment.util.SearchUiUtils;
import com.ximalaya.ting.android.record.manager.RecordConfigureManager;
import com.ximalaya.ting.android.record.manager.RecordTraceManager;
import com.ximalaya.ting.android.record.manager.upload.RecordUploadManager;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by wenbin.liu on 2020-08-21
 *
 * <AUTHOR>
 */
public abstract class BaseTabFragment extends BaseFragment2 implements
        TextView.OnEditorActionListener, TextWatcher,
        View.OnClickListener ,ICommunicateListener,
        IRefreshLoadMoreListener, AdapterView.OnItemClickListener,
        AbsListView.OnScrollListener {

    protected static final int MODE_NORMAL = 0;
    protected static final int MODE_SEARCH = 1;
    //参数：状态过滤
    public static final String KEY_STATUS_TYPE = "key_status_type";
    protected boolean mIsLoading;
    protected int mPageId = 1;
    protected boolean mIsRefresh;
    protected boolean mIsLoadMore;
    protected boolean mIsExpliclitSearch = true; // 区分为了输入文字时的搜索不展示空态；主动搜索才展示空态
    protected boolean mIsFirstResume = true;

    protected RefreshLoadMoreListView mRvListView;
    protected HolderAdapter mAdapter;
    protected List mDataList = new ArrayList<>();

    private RelativeLayout mRootViewGroup;
    private View mFilterView;
    private View mSearchView;
    private ImageView mSearchIv;
    private TextView mCancelTv;
    private ListView mFilterListView;
    private View mSelectView;
    protected TextView mFilterPrimaryTv;
    protected TextView mFilterSecondTv;
    protected EditText mSearchTextEt;
    private TextView mAuditingRule;
    private ViewGroup mVgDraftEntrance;
    private TextView mTvDraftCount;
    private TextView mTvJumpToDraft;
    private ImageView mClearIv;
    protected SingleSelectAdapter mFilterAdapter;
    private List<SingleSelectAdapter.Item> mStatusList = new ArrayList<>(5);
    private List<SingleSelectAdapter.Item> mVideoList = new ArrayList<>(3);
    private boolean mIsPrimaryActive, mIsSecondActive;
    private int C_INACTIVE;
    private int C_FE5E3F;

    protected StatusType mCurStatusType = StatusType.ALL;
    protected VideoType mCurVideoType = VideoType.DUB_VIDEO;

    protected IParentListener mParentListener;
    protected int mCurQueryMode = MODE_NORMAL;
    protected String mSearchKey = "";
    private InputMethodManager mInputMethodManager;
    private boolean mDraftHasEditRecord;
    protected long mServerTimeStamp;

    private AdapterView.OnItemClickListener mSelectItemListener = new AdapterView.OnItemClickListener() {
        @Override
        public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
            SingleSelectAdapter.Item item = mFilterAdapter.getListData().get(position);
            TextView textView;
            if (item.data instanceof StatusType) {
                textView = mFilterPrimaryTv;
                setStatusType((StatusType)item.data);
            }
            else if (item.data instanceof VideoType) {
                textView = mFilterSecondTv;
                setVideoType((VideoType)item.data);
            }
            else {
                return;
            }
            handleFilterSelected(textView, item.data);
            handleFilterWrapAll();
        }
    };

    public BaseTabFragment() {
        super(AppConstants.isPageCanSlide, null);
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    @Override
    protected String getPageLogicName() {
        return this.getClass().getSimpleName();
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        initFilterView();
        mRvListView = findViewById(R.id.record_rf_lv);
        mRvListView.setMode(PullToRefreshBase.Mode.PULL_FROM_START);
        mRvListView.setOnRefreshLoadMoreListener(this);
        mRvListView.setOnItemClickListener(this);
        mRvListView.addOnScrollListener(this);
        mRootViewGroup = findViewById(R.id.record_base_tab_rl);
        mInputMethodManager = (InputMethodManager) mActivity.getSystemService(Context.INPUT_METHOD_SERVICE);
    }

    private void initFilterView() {
        C_INACTIVE = mContext.getResources().getColor(com.ximalaya.ting.android.host.R.color.host_color_666666_888888);
        C_FE5E3F = mContext.getResources().getColor(R.color.record_color_FE5E3F);
        mFilterView = findViewById(R.id.record_filter_container_ll);
        mSearchView = findViewById(R.id.record_search_container_ll);
        mCancelTv = findViewById(R.id.record_tv_cancel_search);
        mCancelTv.setOnClickListener(this);
        mSearchIv = findViewById(R.id.record_search_iv);
        mSearchIv.setOnClickListener(this);
        mSelectView = findViewById(R.id.record_filter_option_container_fl);
        mSelectView.setOnClickListener(this);
        mFilterPrimaryTv = findViewById(R.id.record_filter_title_1);
        mFilterPrimaryTv.setOnClickListener(this);
        mFilterSecondTv = findViewById(R.id.record_filter_title_2);
        mFilterSecondTv.setOnClickListener(this);
        mFilterListView = findViewById(R.id.record_filter_option_lv);
        mFilterAdapter = new SingleSelectAdapter(mContext, new ArrayList<>());
        mFilterListView.setAdapter(mFilterAdapter);
        mFilterListView.setOnItemClickListener(mSelectItemListener);
        mSearchTextEt = findViewById(R.id.record_et_search_content);
        mClearIv = findViewById(R.id.record_clear_search_text);
        mClearIv.setOnClickListener(this);
        OnEditorActionListenerWrapper onEditorActionListenerWrapper = new OnEditorActionListenerWrapper(this);
        EditTextWatcherWrapper editTextWatcherWrapper = new EditTextWatcherWrapper(this);
        mSearchTextEt.setOnEditorActionListener(onEditorActionListenerWrapper);
        mSearchTextEt.addTextChangedListener(editTextWatcherWrapper);
        mSearchTextEt.setOnClickListener(this);

        mAuditingRule = findViewById(R.id.record_auditing_rule);

        mVgDraftEntrance = findViewById(R.id.main_cl_draft_entrance);
        mTvDraftCount = findViewById(R.id.main_tv_draft_count);
        mTvJumpToDraft = findViewById(R.id.main_tv_jump_to_draft);
        mTvJumpToDraft.setOnClickListener(this);

        int statusType = 0;
        Bundle bundle = getArguments();
        if (bundle != null) {
            statusType = bundle.getInt(KEY_STATUS_TYPE, 0);
        }
        //（默认0）：0-全部，1-进行中，2-已通过，3-未通过
        switch (statusType) {
            case 4:
                mCurStatusType = StatusType.SCHEDULED;
                mFilterPrimaryTv.setText(StatusType.SCHEDULED.getName());
                break;
            case 3:
                mCurStatusType = StatusType.FAIL;
                mFilterPrimaryTv.setText(StatusType.FAIL.getName());
                break;
            case 2:
                mCurStatusType = StatusType.SUCCESS;
                mFilterPrimaryTv.setText(StatusType.SUCCESS.getName());
                break;
            case 1:
                mCurStatusType = StatusType.ONGOING;
                mFilterPrimaryTv.setText(StatusType.ONGOING.getName());
                break;
            default:
                mCurStatusType = StatusType.ALL;
                mFilterPrimaryTv.setText(StatusType.ALL.getName());
                break;
        }
        //未通过，显示平台审核规则
        if (mAuditingRule != null) {
            if (StatusType.FAIL.equals(mCurStatusType)) {
                ShowAuditingRule();
            } else {
                mAuditingRule.setVisibility(View.GONE);
            }
        }
        mStatusList.add(new SingleSelectAdapter.Item(StatusType.ALL, statusType == 0));
        mStatusList.add(new SingleSelectAdapter.Item(StatusType.ONGOING, statusType == 1));
        mStatusList.add(new SingleSelectAdapter.Item(StatusType.SUCCESS, statusType == 2));
        mStatusList.add(new SingleSelectAdapter.Item(StatusType.FAIL, statusType == 3));
        if (hasScheduleStatus()) {
            mStatusList.add(new SingleSelectAdapter.Item(StatusType.SCHEDULED, statusType == 4));
        }
        mVideoList.add(new SingleSelectAdapter.Item(VideoType.DUB_VIDEO, true));
        mVideoList.add(new SingleSelectAdapter.Item(VideoType.SHORT_VIDEO, false));
        mVideoList.add(new SingleSelectAdapter.Item(VideoType.PROGRAM_VIDEO, false));
    }

    private void updateDraftEntranceView() {
        if (!isShowDraftEntranceView()) {
            return;
        }
        RecordUploadManager.getInstance().getDrafts(result -> doAfterAnimation(() -> {
            if (canUpdateUi()) {
                boolean hasDraft = !ToolUtil.isEmptyCollects(result);
                if (hasDraft) {
                    for (Record record : result) {
                        if (record != null && record.isEditRecord()) {
                            mDraftHasEditRecord = true;
                            break;
                        }
                    }
                }
                ViewStatusUtil.setVisible(hasDraft ? View.VISIBLE : View.GONE, mVgDraftEntrance);
                if (mTvDraftCount != null) {
                    mTvDraftCount.setText(String.format("本地草稿 %s", hasDraft ? result.size() : ""));
                }
                if (ViewStatusUtil.isViewVisible(mVgDraftEntrance)) {
                    RecordTraceManager.Companion.traceMyProgramTopDraftGuideExplore();
                }
            }
        }), true);
    }

    boolean isShowDraftEntranceView() {
        return false;
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        updateDraftEntranceView();
//        if (ViewStatusUtil.isViewVisible(mVgDraftEntrance) && !mIsFirstResume) {
//            RecordTraceManager.Companion.traceMyProgramTopDraftGuideExplore();
//        }
        mIsFirstResume = false;
    }

    @Override
    protected void loadData() {
        doAfterAnimation(() -> {
                    onPageLoadingCompleted(LoadCompleteType.LOADING);
                    onRefresh();
                });
    }

    @Override
    public void onMore() {
        mIsRefresh = false;
        mIsLoadMore = true;
        mIsExpliclitSearch = true;
        loadDataList();
    }

    public void onRefresh() {
        mPageId = 1;
        if (!ToolUtil.isEmptyCollects(mDataList)) {
            mDataList.clear();
        }
        if (mAdapter != null) {
            mAdapter.clear();
        }
        mIsRefresh = true;
        mIsLoadMore = false;
        mIsExpliclitSearch = true;
        loadDataList();
    }

    @Override
    public void onScrollStateChanged(AbsListView view, int scrollState) {
        if (scrollState == SCROLL_STATE_TOUCH_SCROLL) {
            if (mInputMethodManager != null && mInputMethodManager.isActive()) {
                hideSoftInput();
            }
        }
    }

    @Override
    public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
    }

    protected abstract void loadDataList();

    // 专辑、声音复用审核状态
    protected int getCommonStatus() {
        int status = -1;
        if (mCurStatusType == StatusType.FAIL) {
            status = Track.STATUS_FAIL;
        } else if (mCurStatusType == StatusType.ONGOING) {
            status = Track.STATUS_ING;
        } else if (mCurStatusType == StatusType.SUCCESS) {
            status = Track.STATUS_SUCCESS;
        } else if (mCurStatusType == StatusType.SCHEDULED) {
            status = Record.STATUS_SCHEDULED;
        }
        return status;
    }

    @Override
    public void onClick(View v) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return;
        }
        int id = v.getId();
        if (id == R.id.record_search_iv) {
            setQueryMode(MODE_SEARCH, false);
            SearchUiUtils.setVisible(View.INVISIBLE, mFilterView, mSelectView, mSearchIv);
            mSearchView.setVisibility(View.VISIBLE);
            mSearchTextEt.setCursorVisible(true);
            openSoftInput();
        }
        else if (id == R.id.record_tv_cancel_search) {
            handleCancelSearchClick();
        }
        else if (id == R.id.record_et_search_content) {
            String content = SearchUiUtils.getEditTextContent(mSearchTextEt);
            if (!TextUtils.isEmpty(content)) {
                mSearchTextEt.setSelection(content.length());
            }
            openSoftInput();
        }
        else if (id == R.id.record_clear_search_text) {
            if (mSearchTextEt != null) {
                mSearchTextEt.setText("");
            }
            mSearchKey = "";
        }
        else if (id == R.id.record_filter_option_container_fl) {
            handleFilterWrapAll();
        }
        else if (id == R.id.record_filter_title_1) {
            if (mIsSecondActive) {
                mIsSecondActive = false;
                handleFilterWrapped(mFilterSecondTv);
            }
            mIsPrimaryActive = !mIsPrimaryActive;
            if (mIsPrimaryActive) {
                mFilterAdapter.setListData(mStatusList);
                handleFilterSelected(mFilterPrimaryTv, mCurStatusType);
            } else {
                handleFilterWrapped(mFilterPrimaryTv);
            }

        }
        else if (id == R.id.record_filter_title_2) {
            if (mIsPrimaryActive) {
                mIsPrimaryActive = false;
                handleFilterWrapped(mFilterPrimaryTv);
            }
            mIsSecondActive = !mIsSecondActive;
            if (mIsSecondActive) {
                mFilterAdapter.setListData(mVideoList);
                handleFilterSelected(mFilterSecondTv, mCurVideoType);
            } else {
                handleFilterWrapped(mFilterSecondTv);
            }
        } else if (id == R.id.main_tv_jump_to_draft) {
            if (RecordConfigureManager.isUseOldDraft()) {
                startFragment(MyDraftFragment.newInstance());
            } else {
                startFragment(MyDraftFragmentNew.Companion.newInstance(mDraftHasEditRecord ?
                        MyDraftFragmentNew.TAB_CLIP_INDEX :
                        MyDraftFragmentNew.TAB_RECORD_INDEX));
            }
            RecordTraceManager.Companion.traceMyProgramTopDraftGuideClick();
        }
    }

    @Override
    protected void addLoadStateView(ViewGroup parent, View addView, ViewGroup.LayoutParams lp, LoadCompleteType type) {
        if (mRootViewGroup instanceof RelativeLayout) {
            RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            params.addRule(RelativeLayout.CENTER_IN_PARENT);
            mRootViewGroup.addView(addView, params);
        }
    }

    @Override
    public boolean onBackPressed() {
        hideSoftInput();
        return super.onBackPressed();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mSearchTextEt != null) {
            mSearchTextEt.clearFocus();
            mSearchTextEt.setOnClickListener(null);
            mSearchTextEt.setCursorVisible(false);
            mSearchTextEt.setOnEditorActionListener(null);
        }
        mRvListView.removeOnScrollListener(this);
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable s) {
        mIsExpliclitSearch = false;
        mSearchKey = s.toString();
        if (TextUtils.isEmpty(mSearchKey)) {
            SearchUiUtils.setVisible(View.INVISIBLE, mClearIv);
            openSoftInput();
        } else {
            SearchUiUtils.setVisible(View.VISIBLE, mClearIv);
        }
        loadQuery(mSearchKey);
    }

    public void hideSoftInput() {
        if (mSearchTextEt != null && mActivity != null && canUpdateUi()) {
            mSearchTextEt.clearFocus();
            mSearchTextEt.setCursorVisible(false);
            mInputMethodManager = (InputMethodManager) mActivity.getSystemService(Context.INPUT_METHOD_SERVICE);
            if (mInputMethodManager != null) {
                mInputMethodManager.hideSoftInputFromWindow(mSearchTextEt.getWindowToken(), 0);
            }
        }
    }

    private void openSoftInput() {
        if (mSearchTextEt == null || !canUpdateUi() || mActivity == null) {
            return;
        }
        mSearchTextEt.requestFocus();
        mSearchTextEt.setCursorVisible(true);
        if (mInputMethodManager != null) {
            mInputMethodManager.showSoftInput(mSearchTextEt, 0);
        }
    }

    protected void setQueryMode(int mode, boolean shouldLoadData) {
        mCurQueryMode = mode;
        if (mode == MODE_NORMAL) {
            if (shouldLoadData) {
                onRefresh();
            }
        } else if (mode == MODE_SEARCH) {
            if (mAdapter != null) {
                mAdapter.clear();
            }
            if (mRvListView != null) {
                mRvListView.resetState();
            }
        }
    }

    protected void loadQuery(String keyWord) {
        if (TextUtils.isEmpty(keyWord)) {
            if (mAdapter != null) {
                mAdapter.clear();
            }
            if (mRvListView != null) {
                mRvListView.resetState();
            }
            return;
        }
        onRefresh();
    }

    public void setStatusType(StatusType statusType) {
        mCurStatusType = statusType;
        onRefresh();
    }

    public void setVideoType(VideoType videoType) {
        mCurVideoType = videoType;
        onRefresh();
    }

    private void handleCancelSearchClick() {
        mSearchTextEt.setText("");
        mSearchView.setVisibility(View.INVISIBLE);
        SearchUiUtils.setVisible(View.VISIBLE, mFilterView, mSearchIv);
        hideSoftInput();
        setQueryMode(MODE_NORMAL, true);
    }

    private void check2WrapSearchComponentsIfActive() {
        if (mSearchView != null && mSearchView.getVisibility() == View.VISIBLE) {
            handleCancelSearchClick();
        }
    }

    protected void handleFilterWrapAll() {
        if (mIsPrimaryActive) {
            mIsPrimaryActive = false;
            handleFilterWrapped(mFilterPrimaryTv);
        }
        if (mIsSecondActive) {
            mIsSecondActive = false;
            handleFilterWrapped(mFilterSecondTv);
        }
    }

    protected void handleFilterSelected(TextView textView, Object targetItem) {
        handleSelectSingleItem(textView, true, targetItem);
    }

    private void handleFilterWrapped(TextView textView) {
        handleSelectSingleItem(textView, false, null);
    }

    private void handleSelectSingleItem(TextView textView, boolean isActive, Object targetItem) {
        textView.setTextColor(isActive ? C_FE5E3F : C_INACTIVE);
        textView.setCompoundDrawablesWithIntrinsicBounds(0, 0, isActive ? R.drawable.record_ic_up_orange : R.drawable.record_ic_down_black, 0);
        if (isActive) {
            String result = "";
            if (targetItem instanceof StatusType) {
                result = ((StatusType) targetItem).getName();
            } else if (targetItem instanceof VideoType) {
                result = ((VideoType) targetItem).getName();
            }
            textView.setText(result);
            //未通过，显示平台审核规则
            if(mAuditingRule!=null) {
                if (StatusType.FAIL.getName().contentEquals(result)) {
                    ShowAuditingRule();
                } else {
                    mAuditingRule.setVisibility(View.GONE);
                }
            }
            mFilterAdapter.setSelectType(targetItem);
            mSelectView.setVisibility(View.VISIBLE);
            mSelectView.bringToFront();
        } else {
            mSelectView.setVisibility(View.INVISIBLE);
        }
        textView.setContentDescription(isActive ? "收起" : "展开" +
                (TextUtils.isEmpty(textView.getText()) ? "" : textView.getText().toString()));
    }

    private void ShowAuditingRule() {
        mAuditingRule.setVisibility(View.VISIBLE);
        if (TextUtils.isEmpty(mAuditingRule.getText())) {
            try {
                SpannableString rule = new SpannableString("查看平台审核规则，了解详细下架原因");
                ClickableSpan clickableSpan = new ClickableSpan() {
                    @Override
                    public void onClick(@NonNull View widget) {
                        startFragment(NativeHybridFragment.newInstance("https://m.ximalaya.com/helper/questionPage?questionId=104", true));
                    }

                    @Override
                    public void updateDrawState(@NonNull TextPaint ds) {
                        super.updateDrawState(ds);
                        ds.setColor(getResources().getColor(R.color.record_blue_4990E2));
                        ds.setUnderlineText(false);
                    }
                };
                rule.setSpan(clickableSpan, rule.length() - 4, rule.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                mAuditingRule.setMovementMethod(LinkMovementMethod.getInstance());
                mAuditingRule.setText(rule);
            }catch (Exception e){
                e.printStackTrace();
            }
        }
    }

    @Override
    public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
        if (!TextUtils.isEmpty(mSearchKey) && actionId == EditorInfo.IME_ACTION_SEARCH) {
            loadQuery(mSearchKey);
        }
        mIsExpliclitSearch = true;
        hideSoftInput();
        return true;
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (!canUpdateUi()) {
            return;
        }
        if (!isVisibleToUser) {
            hideSoftInput();
            check2WrapSearchComponentsIfActive();
        } else {
            updateDraftEntranceView();
        }
    }

    protected void handleOnStatusTypeCountChange(int totalCount, int rejectedCount, int approvingCount, int approvedCount) {
        handleOnStatusTypeCountChange(totalCount, rejectedCount, approvingCount, approvedCount, -1);
    }
    protected void handleOnStatusTypeCountChange(int totalCount, int rejectedCount, int approvingCount, int approvedCount, int scheduledCount) {
        if (mParentListener != null) {
            mParentListener.onTotalCountUpdate(this, totalCount);
        }
        if (!ToolUtil.isEmptyCollects(mStatusList)) {
            mStatusList.clear();
        }
        mStatusList.add(new SingleSelectAdapter.Item(StatusType.ALL,false, totalCount));
        mStatusList.add(new SingleSelectAdapter.Item(StatusType.ONGOING,false, approvingCount));
        mStatusList.add(new SingleSelectAdapter.Item(StatusType.SUCCESS,false, approvedCount));
        mStatusList.add(new SingleSelectAdapter.Item(StatusType.FAIL,false, rejectedCount));
        if (hasScheduleStatus()) {
            mStatusList.add(new SingleSelectAdapter.Item(StatusType.SCHEDULED, false, scheduledCount));
        }
        if (mFilterAdapter != null) {
            mFilterAdapter.setSelectType(mCurStatusType);
            mFilterAdapter.notifyDataSetChanged();
        }
    }

    protected boolean hasScheduleStatus() {
        return false;
    }

    @Override
    protected void loadDataOk() {
        if (mRvListView != null) {
            mRvListView.setVisibility(View.VISIBLE);
        }
    }

    @Override
    protected void loadDataError() {
        if (ToolUtil.isEmptyCollects(mDataList)) {
            if (mRvListView != null) {
                mRvListView.setVisibility(View.GONE);
            }
        }
    }


    @Override
    public int getContainerLayoutId() {
        return R.layout.record_fra_my_prog_rf_lv;
    }

    @Override
    public void setListener(IParentListener listener) {
        mParentListener = listener;
    }
}
