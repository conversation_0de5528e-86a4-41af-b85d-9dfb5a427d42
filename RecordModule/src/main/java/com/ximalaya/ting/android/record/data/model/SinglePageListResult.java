package com.ximalaya.ting.android.record.data.model;

import java.util.List;

/**
 * Created by ervin.li on 2018/4/26.
 * <AUTHOR>
 */

/**
 * 单页数据模型
 * @param <T> 数据元素
 * <AUTHOR>
 */
public class SinglePageListResult<T> {

    private int code;
    private String msg;
    private List<T> data;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }
}
