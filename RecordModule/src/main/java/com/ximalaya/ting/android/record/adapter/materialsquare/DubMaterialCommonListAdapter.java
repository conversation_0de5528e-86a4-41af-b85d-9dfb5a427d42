package com.ximalaya.ting.android.record.adapter.materialsquare;

import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager;
import com.ximalaya.ting.android.host.model.materialsquare.DubMaterialBean;
import com.ximalaya.ting.android.host.model.play.DubTransferModel;
import com.ximalaya.ting.android.host.util.common.StringUtil;
import com.ximalaya.ting.android.host.util.common.TimeHelper;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.record.R;
import com.ximalaya.ting.android.record.data.model.square.CategoryTagInfo;
import com.ximalaya.ting.android.record.fragment.dub.DubMaterialDownloadFragment;
import com.ximalaya.ting.android.record.fragment.dub.ImageDubFragment;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;

import java.util.List;

/**
 * Created by zhangkaikai on 2018/10/17.
 *
 * <AUTHOR>
 */
public class DubMaterialCommonListAdapter extends HolderAdapter<DubMaterialBean> {
    private final BaseFragment2 fragment;
    private CategoryTagInfo mInfo;

    public DubMaterialCommonListAdapter(BaseFragment2 fragment, List<DubMaterialBean> listData, CategoryTagInfo info) {
        super(fragment.getContext(), listData);
        this.fragment = fragment;
        this.mInfo = info;
    }

    @Override
    public void onClick(View view, final DubMaterialBean bean, int position, BaseViewHolder holder) {
        BaseFragment2 dubFragment;
        if (bean.getType() == 0) {
            dubFragment = ImageDubFragment.newInstance(bean.getTemplateId(), 0, null);
            fragment.startFragment(dubFragment);
        } else {
            if (ChildProtectManager.checkChildrenModeOpenFromToB(context)) {
                return;
            }
            DubTransferModel dubTransferModel = new DubTransferModel.DubTransferItemBuilder()
                .seTrackId(bean.getTrackId())
                .setTopicId(0)
                .setTopicName(null)
                .setTopicUploadType(0)
                .setFromType(1)
                .setUp();
            BaseFragment baseFragment = DubMaterialDownloadFragment.newInstance(dubTransferModel);
            fragment.startFragment(baseFragment);
        }

        UserTracking tracking = new UserTracking("趣配音素材列表页", "button").setSrcModule("tag").setItemId("立即演绎")
                .setSrcPosition(position + 1).setDubId(bean.getTrackId());
        if (mInfo.categoryId > 0) {
            tracking.setDubCategory(mInfo.categoryId);
        } else if (!TextUtils.isEmpty(mInfo.categoryName)) {
            tracking.setDubCategory(mInfo.categoryName);
        }

        if (!TextUtils.isEmpty(mInfo.subCategoryName)) {
            tracking.setTagId(mInfo.subCategoryName);
        }
        tracking.statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
    }

    @Override
    public int getConvertViewId() {
        return R.layout.record_material_category_detail_template;
    }

    @Override
    public BaseViewHolder buildHolder(View convertView) {
        return new DubMaterialDetailListHolder(convertView);
    }

    @Override
    public void bindViewDatas(BaseViewHolder holder, final DubMaterialBean itemModel, final int position) {
        if (itemModel == null) return;

        DubMaterialDetailListHolder itemHolder = (DubMaterialDetailListHolder) holder;

        if (position == 0) {
            itemHolder.divider.setVisibility(View.GONE);
        } else {
            itemHolder.divider.setVisibility(View.VISIBLE);
        }

        int iconRes;
        if (itemModel.getType() == 0) {
            iconRes = R.drawable.record_ic_pic_material;
            itemHolder.tvDuration.setText("共" + itemModel.getPictureCount() + "页");
        } else {
            iconRes = com.ximalaya.ting.android.host.R.drawable.host_ic_video;
            itemHolder.tvDuration.setText("时长:  " + TimeHelper.toTime(itemModel.getDuration() / 1000f));
        }
        itemHolder.ivType.setImageResource(iconRes);
        itemHolder.tvDubName.setText(itemModel.getName());
        if (itemModel.getDubbedCount() > 0) {
            itemHolder.tvDubCount.setText(StringUtil.getFriendlyNumStrAndCheckIsZero(itemModel.getDubbedCount(), " 演绎"));
            itemHolder.tvDubCount.setVisibility(View.VISIBLE);
        } else {
            itemHolder.tvDubCount.setVisibility(View.GONE);
            itemHolder.tvDubCount.setText("");
        }

        setClickListener(itemHolder.ivDubNow, itemModel, position, itemHolder);
        AutoTraceHelper.bindData(itemHolder.ivDubNow, new AutoTraceHelper.DataWrap(position, itemModel));
        ImageManager.from(context).displayImage(itemHolder.ivCoverImage, itemModel.getSurfaceUrl(), com.ximalaya.ting.android.host.R.drawable.host_default_focus_img);

        List<DubMaterialBean.TagsBean> tags = itemModel.getTags();
        if (ToolUtil.isEmptyCollects(tags)) {
            itemHolder.tvDubLabels.setVisibility(View.GONE);
        } else {
            itemHolder.tvDubLabels.setVisibility(View.VISIBLE);
            StringBuilder sb = new StringBuilder();
            for (int j = 0; j < tags.size(); j++) {
                DubMaterialBean.TagsBean tagsBean = tags.get(j);
                if (tagsBean == null || TextUtils.isEmpty(tagsBean.getName())) {
                    continue;
                }
                if (j == tags.size() - 1) {
                    sb.append(tagsBean.getName());
                    break;
                }
                sb.append(tagsBean.getName()).append(" · ");
            }
            itemHolder.tvDubLabels.setText(sb);
        }
    }


    public static class DubMaterialDetailListHolder extends HolderAdapter.BaseViewHolder {
        ImageView ivCoverImage;
        ImageView ivType;
        TextView tvDubCount;
        TextView tvDubName;
        ImageView ivDubNow;
        TextView tvDuration;
        TextView tvDubLabels;
        View divider;

        public DubMaterialDetailListHolder(View convertView) {
            ivCoverImage = (ImageView) convertView.findViewById(R.id.record_iv_dub_cover);
            ivType = (ImageView) convertView.findViewById(R.id.record_iv_dub_type);
            ivDubNow = (ImageView) convertView.findViewById(R.id.record_dub_now);
            tvDubCount = (TextView) convertView.findViewById(R.id.record_tv_dub_count);
            tvDubName = (TextView) convertView.findViewById(R.id.record_material_dub_name);
            tvDuration = (TextView) convertView.findViewById(R.id.record_tv_dub_duration);
            tvDubLabels = (TextView) convertView.findViewById(R.id.record_material_dub_label);
            divider = convertView.findViewById(R.id.record_dub_material_template_divider);
        }
    }
}
